<template>
  <div class="goodsmam-table">
    <a-table
      v-model:selected-row-keys="selectedRowKeys"
      row-key="key"
      :columns="systemPageColumns"
      :data-source="tableData"
      :loading="isLoading"
      :expand-row-by-click="true"
      :pagination="false"
      :row-class-name="rowClassName"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'radio'">
          <a-radio
            v-if="record.link"
            :checked="selectedRow?.key === record.key"
            @change="() => handleSelect(record)"
          />
        </template>
      </template>
    </a-table>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
// import { getListCategorySystemPage } from '@/api/decoration';

const emit = defineEmits(['onFormData']);
const selectedRowKeys = ref<any[]>([]);
const selectedRow = ref<any>({});
const isLoading = ref<boolean>(false);

const systemPageColumns = [
  {
    title: "",
    dataIndex: "radio",
    key: "radio",
    align: "left",
    width: 80,
  },
  {
    title: "本地生活",
    dataIndex: "title",
    key: "title",
    align: "left",
  },
];
const tableData = ref<any[]>([]);

// 模拟接口数据
const getMockSystemPageData = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        code: 200,
        message: 'Success',
        data: [
          {
            categoryName: '点餐',
            systemPages: [
              { pageName: '瑞幸', link: '/luckin' },
              { pageName: '星巴克', link: '/starbucks' },
              { pageName: '库迪', link: '/cotti' },
              { pageName: '奈雪', link: '/nayuki' },
              { pageName: '必胜客', link: '/pizzahut' },
              { pageName: '肯德基', link: '/kfc' },
              { pageName: '麦当劳', link: '/mcdonalds' },
            ],
          },
          {
            categoryName: '生活',
            systemPages: [
              { pageName: '美团外卖', link: '/meituan/waimai' },
              { pageName: '小象超市', link: '/xiaoxiang/supermarket' },
              { pageName: '美团团购', link: '/meituan/groupbuy' },
              { pageName: '新房楼盘（部分需授权登录）', link: '/newhouse' },
            ],
          },
          {
            categoryName: '出行',
            systemPages: [
              { pageName: '曹操出行（需授权）', link: '/caocao' },
            ],
          },
          {
            categoryName: '零售',
            systemPages: [{ pageName: '同城专区', link: '/city/retail' }],
          },
        ],
      };
      resolve(mockData);
    }, 200);
  });
};


const fetchData = async () => {
  isLoading.value = true;
  try {
    // const res: any = await getListCategorySystemPage();
    const res: any = await getMockSystemPageData();
    if (res.code === 200) {
      tableData.value = res.data.map((category, index) => ({
        key: `category_${index}`,
        title: category.categoryName,
        children: category.systemPages.map((page, subIndex) => ({
          key: `page_${index}_${subIndex}`,
          title: page.pageName,
          link: page.link,
          ...page,
        })),
      }));
    }
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

const handleSelect = (record) => {
  if (record.link) {
    selectedRow.value = record;
    selectedRowKeys.value = [record.key];
    emit('onFormData', {
      enums: 'SYSTEM_PAGE',
      name: record.title,
      uri: record.link,
    });
  }
};

const rowClassName = (record) => {
  const classNames = [];
  if (record.link) {
    classNames.push('child-row');
  }
  if (selectedRow.value && record.key === selectedRow.value.key) {
    classNames.push('selected-row');
  }
  return classNames.join(' ');
};

const resetData = () => {
  selectedRow.value = {};
  selectedRowKeys.value = [];
  if (tableData.value.length === 0) {
    fetchData();
  }
}

onMounted(() => {
  fetchData();
});

defineExpose({ resetData });
</script>
<style lang="less" scoped>
.goodsmam-table {
  margin-top: 24px;
}

/* 隐藏子节点的缩进和展开图标，以实现更好的对齐 */
:deep(.child-row .ant-table-row-indent),
:deep(.child-row .ant-table-row-expand-icon) {
  display: none !important;
}

:deep(.selected-row > td),
:deep(.selected-row:hover > td) {
  background-color: #E6F4FF !important;
}
</style> 