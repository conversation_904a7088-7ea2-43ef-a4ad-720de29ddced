<template>
  <div :style="{ background: bgColor }" class="container">
    <div style="position: fixed; z-index: 10">
      <div :style="{ background: bgColor }" class="search-box flex">
        <div :style="{ color: info.textColor }" class="location">闵行区</div>
        <div :style="{ borderTopColor: info.textColor }" class="triangle"></div>
        <div
          :style="{
            borderColor: info.bgColor === '#fff' ? '#eee' : 'transparent',
          }"
          class="s-box flex"
        >
          <img
            :src="`${VITE_API_IMG}/2024/08/52326ba530b54688b610b09bcd9198cd.png`"
            alt=""
            class="icon"
          />
          <div class="text">搜索</div>
        </div>
        <img
          :src="`${VITE_API_IMG}/2024/08/f1c9d6683b5a4c8bb8a6f67c738d6f44.png`"
          alt=""
          class="jiaonang"
        />
      </div>
    </div>
    <img
      class="icon-pic"
      :src="`${VITE_API_IMG}/2024/08/f070bcf6e9f546c7ac8ee2eb1f23595b.png`"
      alt=""
    />
  </div>
</template>

<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref, watchEffect } from "vue";
import { getDecorationStore } from "@/store";

const decorationStore = getDecorationStore();

const info = ref<any>({});
const bgColor = ref<string>("");
const detailData = decorationStore.decorationInfo.components.find(
  (item: any) => item.templateId === "homePageTitle"
);

watchEffect(() => {
  info.value = detailData.info || {};
  if (detailData.info && detailData.info.bgColor) {
    bgColor.value = detailData.info.isTransparent
      ? "#01D47B"
      : detailData.info.bgColor;
  } else {
    bgColor.value = detailData.info.isTransparent ? "#01D47B" : "#fff";
  }
});
</script>

<style lang="less" scoped>
.container {
  padding-bottom: 4px;
}
.search-box {
  width: 369px;
  height: 56px;
  padding: 10px;
  justify-content: space-between;
  .location {
    font-weight: bold;
    font-size: 16px;
    color: #172119;
  }

  .triangle {
    width: 8px;
    height: 10px;
    border-top: 5px solid #172119;
    border-right: 4px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 4px solid transparent;
    margin-left: -12px;
    margin-top: 6px;
  }

  .s-box {
    width: 150px;
    height: 32px;
    background: #fff;
    border-radius: 8px;
    padding-left: 12px;
    border: 1px solid #fff;

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }

    .text {
      font-size: 14px;
      color: #616a66;
    }
  }

  .jiaonang {
    width: 90px;
    height: auto;
  }
}

.icon-pic {
  width: 100%;
  height: auto;
  padding: 0 10px;
  margin-top: 56px;
}
</style>
