import Layout from "@/layouts/index.vue";
import cmsIcon from "@/assets/images/cms_icon.svg?url";
import actcmsIcon from "@/assets/images/cms_icon_act.svg?url";

export const cmsManage = {
  path: "/cms",
  component: Layout,
  name: "cms",
  meta: {
    title: "CMS管理",
    icon: cmsIcon,
    activeIcon: actcmsIcon,
    expanded: false,
    state: "IS_SHOW",
  },
  nameEn: "npCmsManagement",
  children: [
    {
      path: "index",
      name: "pageLayoutIndex",
      component: () => import("@/pages/cms/pageLayout/index.vue"),
      nameEn: "npPageConfig",
      meta: { title: "页面配置", expanded: true, state: "IS_SPOT" },
    },
    {
      path: "fixedConfig",
      name: "fixedConfig",
      nameEn: "npFixedPageConfig",
      meta: { title: "固定页面配置", state: "IS_SPOT" },
      children: [
        {
          path: "fixedConfigList",
          name: "fixedConfigList",
          component: () => import("@/pages/cms/fixedConfig/index.vue"),
          nameEn: "npFixedPage",
          meta: { title: "支付页" },
        },
      ],
    },
    {
      path: "receptionClassify",
      name: "receptionClassify",
      nameEn: "npFrontEndClassificationConfig",
      meta: { title: "前台分类配置", state: "IS_SPOT" },
      children: [
        {
          path: "receptionList",
          name: "receptionList",
          component: () =>
            import("@/pages/cms/receptionClassify/receptionList/index.vue"),
          nameEn: "npFrontEndClassification",
          meta: { title: "前台分类" },
        },
        {
          path: "admin",
          name: "admin",
          component: () =>
            import("@/pages/cms/receptionClassify/adminCommodity/index.vue"),
          nameEn: "",
          meta: {
            hidden: true,
            replace: "receptionList",
            bread: ["前台分类", "管理"],
          },
        },
        {
          path: "add",
          name: "add",
          component: () =>
            import("@/pages/cms/receptionClassify/addCommodity/index.vue"),
          nameEn: "npBatchAddClassifiedProducts",
          meta: { title: "批量添加分类商品" },
        },
      ],
    },
    {
      path: "commodityInformation",
      name: "commodityInformation",
      component: () => import("@/pages/commodityInformation/index.vue"),
      nameEn: "npProductInfoConfig",
      meta: { title: "商品信息", expanded: true, state: "IS_SPOT" },
    },
    {
      path: "imageConfiguration",
      name: "imageConfiguration",
      component: () => import("@/pages/imageConfiguration/index.vue"),
      nameEn: "npTextureConfig",
      meta: { title: "贴图配置", expanded: true, state: "IS_SPOT" },
    },
    {
      path: "floatLayer",
      name: "floatLayer",
      component: () => import("@/pages/cms/floatLayer/index.vue"),
      nameEn: "npFloatLayer",
      meta: { title: "浮层广告配置", expanded: true, state: "IS_SPOT" },
      children: [
        {
          path: "addAdvert",
          name: "addAdvert1",
          nameEn: "npFloatLayer",
          component: () => import("@/pages/cms/floatLayer/addAdvert.vue"),
          meta: {
            title: "新增广告配置",
            state: "IS_SHOW",
            hidden: true,
            expanded: false,
            replace: "cms/floatLayer",
          },
        },
      ],
    },
    {
      path: "imageSettings",
      name: "imageSettings",
      component: () =>
        import("@/pages/imageConfiguration/components/imageSettings.vue"),
      nameEn: "",
      meta: {
        hidden: true,
        replace: "imageConfiguration",
        bread: ["贴图配置", "设置"],
      },
    },
    {
      path: "searchConfiguration",
      name: "searchConfiguration",
      meta: { title: "搜索配置", state: "IS_SPOT" },
      nameEn: "npSearchConfig",
      children: [
        {
          path: "searchPageConfiguration",
          name: "searchPageConfiguration",
          component: () =>
            import(
              "@/pages/searchConfiguration/searchPageConfiguration/index.vue"
            ),
          nameEn: "npSearchPageConfig",
          meta: { title: "搜索页面配置" },
        },
        {
          path: "searchScrollPhrase",
          name: "searchScrollPhrase",
          component: () =>
            import("@/pages/searchConfiguration/searchScrollPhrase/index.vue"),
          nameEn: "npSearchForScrollingPhrases",
          meta: { title: "搜索滚动词组" },
        },
        {
          path: "settings",
          name: "settings",
          component: () =>
            import(
              "@/pages/searchConfiguration/searchScrollPhrase/components/settings/index.vue"
            ),
          nameEn: "",
          meta: {
            hidden: true,
            replace: "searchScrollPhrase",
            bread: ["搜索滚动词组", "设置"],
          },
        },
        {
          path: "hotWords",
          name: "hotWords",
          component: () =>
            import("@/pages/cms/searchConfiguration/hotWords/index.vue"),
          meta: { title: "热搜词", expanded: true },
          nameEn: "npSearchForScrollingPhrases",
        },
        {
          path: "searchRank",
          name: "searchRank",
          component: () =>
            import("@/pages/cms/searchConfiguration/searchRank/index.vue"),
          meta: { title: "搜索页榜单", expanded: true },
          nameEn: "npSearchForScrollingPhrases",
        },
      ],
    },
    {
      path: "materialCenter",
      name: "materialCenter",
      component: () => import("@/pages/cms/materialCenter/index.vue"),
      nameEn: "npMaterialCenter",
      meta: { title: "素材中心", state: "IS_SPOT" },
    },
    {
      path: "localZone",
      name: "localZone",
      component: () => import("@/pages/cms/localZone/index.vue"),
      nameEn: "npLocalAreaDelivery",
      meta: { title: "同城专区配置", state: "IS_SPOT" },
    },
    {
      path: "editLocalZone",
      name: "editLocalZone",
      component: () => import("@/pages/cms/localZone/editLocalZone.vue"),
      nameEn: "",
      meta: {
        hidden: true,
        replace: "localZone",
        bread: ["同城专区配置", "编辑专区"],
      },
    },
    {
      path: "manageLocalZone",
      name: "manageLocalZone",
      component: () => import("@/pages/cms/localZone/manageLocalZone.vue"),
      nameEn: "",
      meta: {
        hidden: true,
        replace: "localZone",
        bread: ["同城专区配置", "同城专区管理"],
      },
    },
    {
      path: "zoneManagement",
      nameEn: "npZoneManagement",
      name: "zoneManagement",
      component: () => import("@/pages/cms/zoneManagement/index.vue"),
      meta: { title: "专区管理", state: "IS_SPOT" },
    },
    {
      path: "editZone",
      name: "editZone",
      component: () => import("@/pages/cms/zoneManagement/editZone.vue"),
      meta: { title: "编辑专区", expanded: true },
    },
    {
      path: "shopPreferred",
      name: "shopPreferred",
      meta: { title: "我店优选", expanded: true },
      nameEn: "npShopPreferred",
      children: [
        {
          path: "newZone",
          name: "newZone",
          component: () =>
            import("@/pages/cms/shopPreferred/newZone/index.vue"),
          meta: { title: "新专区管理" },
          nameEn: "npNewZone",
          children: [
            {
              path: "newZoneEdit",
              name: "newZoneEdit",
              component: () =>
                import(
                  "@/pages/cms/shopPreferred/newZone/newZoneEdit/index.vue"
                ),
              meta: { title: "编辑专区" },
            },
          ],
        },
      ],
    },
  ],
};
