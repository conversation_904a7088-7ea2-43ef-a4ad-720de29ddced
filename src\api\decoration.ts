import request from "@/request";
import { PaginationResponse, Response } from "./common";
import { decorationContentParams } from "@/pages/cms/decoration/type";

const api = "/life-platform-dashboard";

// 获取-装修详情
export const getDecorationDetail = (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `${api}/decoration/detail?id=${params.id}`,
    data: params,
    headers: { "X-Accept-Version": "yanghongfa" },
  });
// 保存-装修详情
export const saveDecorationDetail = (params: any) => {
  let apiName = "";
  const { scene } = params;
  if (scene === "homePage") {
    apiName = "homePage";
  } else if (
    scene === "subPage" ||
    scene === "offlinePay" ||
    scene === "offlinePayFinish" ||
    scene === "onlinePayFinish"
  ) {
    apiName = "subPage";
  } else if (scene === "firstScreen") {
    apiName = "homepageContent";
  } else if (scene === "goods") {
    apiName = "productContent";
  } else if (scene === "brand") {
    apiName = "brandContent";
  }
  return request<Response<any>>({
    method: "POST",
    path: `${api}/decoration/${apiName}`,
    data: params,
  });
};

// 获取-页面内容列表
export const getPageContentList = (params: decorationContentParams) =>
  request<Response<PaginationResponse<any[]>>>({
    method: "POST",
    path: `${api}/basePage/page?page=${params.page}&size=${params.size}`,
    data: params,
  });

// 模糊查询-搜索页面列表
export const searchPageList = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/searchPage/searchPageList`,
    data: params,
  });
// 模糊查询-滚动词组列表
export const swiperWordsList = (params: any) =>
  request<Response<any>>({
    method: "POST",
    path: `${api}/searchRollingWordsGroup/searchGroupList`,
    data: params,
  });

  /*------CMS2.0版本新增接口------ */
// 获取-系统页分类列表
export const getListCategorySystemPage = (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `${api}/systemPage/listCategorySystemPage`,
    data: params,
  });

// 获取-店铺分类 | 团购分类
export const getShopOrGroupCategories= (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `${api}/systemContentCategory/queryCategories`,
    data: params,
  });


  // 获取组件id.
export const getNextComponentId= (params: any) =>
  request<Response<any>>({
    method: "GET",
    path: `${api}/decoration/nextComponentId`,
    data: params,
  });