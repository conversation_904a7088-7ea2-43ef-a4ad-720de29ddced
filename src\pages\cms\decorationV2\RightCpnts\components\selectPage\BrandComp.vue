<template>
  <div class="brand-container">
    <div class="brand-title">类型</div>
    <div class="brand-radio">
      <a-radio-group
        v-model:value="brandData.type"
        name="brandType"
        :options="brandRadioData"
        @change="brandRadioChange"
      />
    </div>

    <!-- 指定分类 -->
    <template v-if="brandData.type == 1">
      <div class="brand-goods">商品管理</div>
      <div class="brand-select-details" @click="showBrandPopupMethod">
        <div class="select-details-left">选择分类</div>
        <div class="select-details-right">
          <span v-if="brandData.platCategoryName" class="details-span">{{
            brandData.platCategoryName
          }}</span>
          <span v-if="!brandData.platCategoryName">请选择商品内容</span>
          <img
            :src="`${VITE_API_IMG}/2024/08/7c67138a798d4365a216e8f2b9235fd4.png`"
          />
        </div>
      </div>
    </template>

    <!-- 指定品牌 -->
    <template v-if="brandData.type == 2">
      <div class="brand-goods">品牌管理</div>
      <a-button type="primary" size="middle" class="mt10" @click="showBrandNamePopupMethod">
        选择品牌
      </a-button>
      <div v-if="brandData.brands.length > 0" class="brand-info">
        <span>品牌信息</span>
        <span>最多支持100个品牌</span>
      </div>
      <div v-for="(element, index) in brandData.brands" :key="index" class="brand-list">
        <img
          v-if="!element.brandImgUrl"
          class="brand-Image"
          :src="`${VITE_API_IMG}/2024/08/b1ee6ddf0e3f4f11b32b2940fed5e693.png`"
        />
        <img
          v-if="element.brandImgUrl"
          class="brand-Image"
          :src="element.brandImgUrl"
        />
        <div class="brand-box">
          <div class="brand-box-name">
            {{
              element.brandName ? element.brandName : "-------------------"
            }}
          </div>
        </div>
        <img
          class="brand-shut-Img"
          :src="`${VITE_API_IMG}/2024/08/b48cb30452984634ba1c6114202be146.png`"
          @click.stop="shutImage(index)"
        />
      </div>
    </template>
  </div>

  <!-- 品牌平台分类弹框 -->
  <brandSort ref="brandSortRef" @on-brand-call-back="onBrandCallBack" />
  <!-- 品牌商品名称弹框 -->
  <brandName
    ref="brandNameRef"
    @on-brand-name-call-back="onBrandNameCallBack"
  />
</template>

<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { ref, reactive } from "vue";
import { message } from "woody-ui";
import brandSort from "../brandSort.vue";
import brandName from "../brandName.vue";

// 品牌类型选项
const brandRadioData = [
  { label: "指定分类", value: 1 },
  { label: "指定品牌", value: 2 }
];

// 定义emit事件
const emit = defineEmits(['onFormData']);

// 品牌数据
const brandData = reactive({
  type: 1, // 默认指定分类
  platCategoryId: null,
  platCategoryName: null,
  brands: []
});

// 获取子组件方法
type brandSortType = { showBrandSortRef: () => void };
const brandSortRef = ref<brandSortType | null>(null);
type brandNameType = { showBrandNameRef: (data) => void };
const brandNameRef = ref<brandNameType | null>(null);

// 品牌类型切换
const brandRadioChange = () => {
  brandData.platCategoryId = null;
  brandData.platCategoryName = null;
  brandData.brands = [];
  emitBrandData();
};

// 分类选择回调
const onBrandCallBack = (item) => {
  const { id, categoryName } = item;
  brandData.platCategoryId = id;
  brandData.platCategoryName = categoryName;
  emitBrandData();
};

// 显示品牌平台分类组件
const showBrandPopupMethod = () => {
  if (brandSortRef.value) {
    if (typeof brandSortRef.value.showBrandSortRef === "function") {
      brandSortRef.value.showBrandSortRef();
    }
  }
};

// 品牌选择回调
const onBrandNameCallBack = (data) => {
  const uniqueData = [];
  const seenIds = new Set();
  data.forEach((item) => {
    brandData.brands.push({
      brandId: item.brandId,
      order: null,
      brandImgUrl: item.brandUrl,
      brandName: item.brandName,
    });
  });
  brandData.brands.forEach((item) => {
    if (!seenIds.has(item.brandId)) {
      seenIds.add(item.brandId);
      uniqueData.push(item);
    }
  });
  brandData.brands = uniqueData;
  emitBrandData();
};

// 删除品牌信息列表
const shutImage = (index) => {
  brandData.brands.splice(index, 1);
  emitBrandData();
};

// 显示品牌名称组件
const showBrandNamePopupMethod = () => {
  if (brandNameRef.value) {
    if (typeof brandNameRef.value.showBrandNameRef === "function") {
      brandNameRef.value.showBrandNameRef(brandData.brands);
    }
  }
};

// 向父组件发送数据
const emitBrandData = () => {
  // 根据选择类型决定发送的数据
  if (brandData.type === 1 && brandData.platCategoryId) {
    // 指定分类模式
    emit('onFormData', {
      type: brandData.type,
      platCategoryId: brandData.platCategoryId,
      platCategoryName: brandData.platCategoryName,
      name: brandData.platCategoryName,
      enums: 'BRAND_COMPONENT'
    });
  } else if (brandData.type === 2 && brandData.brands.length > 0) {
    // 指定品牌模式
    emit('onFormData', {
      type: brandData.type,
      brands: brandData.brands,
      name: `已选择${brandData.brands.length}个品牌`,
      enums: 'BRAND_COMPONENT'
    });
  }
};
</script>

<script lang="ts">
export default {
  name: 'BrandComp'
}
</script>

<style lang="less" scoped>
.brand-container {
  padding: 16px;
}

.brand-title {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 12px;
}

.brand-radio {
  margin-bottom: 16px;
}

.brand-goods {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 12px;
}

.brand-select-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 16px;

  &:hover {
    border-color: #4080ff;
  }
}

.select-details-left {
  font-size: 14px;
  color: #1d2129;
}

.select-details-right {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #86909c;

  .details-span {
    color: #1d2129;
  }

  img {
    width: 16px;
    height: 16px;
  }
}

.brand-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px 0 12px 0;
  font-size: 12px;
  color: #86909c;
}

.brand-list {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  margin-bottom: 8px;

  .brand-Image {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    margin-right: 12px;
  }

  .brand-box {
    flex: 1;

    .brand-box-name {
      font-size: 14px;
      color: #1d2129;
    }
  }

  .brand-shut-Img {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
}

.mt10 {
  margin-top: 10px;
}
</style>