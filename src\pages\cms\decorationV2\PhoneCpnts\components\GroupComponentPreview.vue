<template>
  <div>
    <a-row class="nav-tab">
      <a-col :span="8">全部分类<img src="@/assets/images/icon_2.png" /></a-col>
      <a-col :span="8">附近<img src="@/assets/images/icon_2.png" /></a-col>
      <a-col :span="8">排序方式<img src="@/assets/images/icon_2.png" /></a-col>
    </a-row>
    <div class="main-card">
      <div class="flex-box">
        <div class="img-css">
          <img src="@/assets/images/small-logo.png" alt="" />
        </div>
        
        <div class="title-box">
          <!-- <img src="@/assets/images/icon_1.png" /> -->
          <div class="title">标题7897978978</div>
          <div class="tag-box">
            <a-tag color="red">今日热抢</a-tag>
            <a-tag color="red">今日热抢</a-tag>
            <a-tag color="red">今日热抢</a-tag>
          </div>
          <div class="desc">
              <span class="span1">含【套餐内容1+套餐内个会员国回国狗容2...</span>
              <span class="address">24.5km</span>
          </div>
          <div class="price-div">
              <div class="money">
                <span>¥25.01</span>
                <span class="small">¥50.00</span>
              </div>
              <div class="money">低至5.9折</div>
              <img src="@/assets/images/icon_1.png" />
          </div>
        </div>
      </div>
      <div class="flex-box">
        <div class="img-css">
          <img src="@/assets/images/small-logo.png" alt="" />
        </div>
        <div class="title-box">
          <div class="title">标题7897标题7897978978标题7897978978978978</div>
          <div class="tag-box">
            <a-tag color="red">今日热抢</a-tag>
            <a-tag color="red">今日热抢</a-tag>
            <a-tag color="red">今日热抢</a-tag>
          </div>
          <div class="desc">
              <span class="span1">含【套餐内容1+套餐内个会员国回国狗容2...</span>
              <span class="address">24.5km</span>
          </div>
          <div class="price-div">
              <div class="money">
                <span>¥25.01</span>
                <span class="small">¥50.00</span>
              </div>
              <div class="money">低至5.9折</div>
              <img src="@/assets/images/icon_1.png" alt="">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup> 
  import { ref } from 'vue';
  const value = ref<number>(2);
</script>
<style scoped lang="less">
.nav-tab{
    text-align: center;
    img{
      height:14px;
    }
}
  .main-card {
    
    
    .flex-box {
      display: flex;
      flex-direction: row;
      margin: 12px 12px;
      .img-css {
        width: 104px;
        height: 104px;
        border-radius: 0px 0px 0px 0px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .title-box {
        width:220px;
        margin-left: 12px;
        .title {
          line-height: 25px;
          padding-bottom: 0;
          width:95%;
          white-space: nowrap;  
            text-overflow: ellipsis;  
            overflow: hidden;
        }
        .tag-box{
          margin-top:5px;
        }
        .desc {
          line-height: 20px;
          color: #666666;
          font-size: 12px;
          margin-top:5px;
          .span1{
            width:80%;
            white-space: nowrap;  
            text-overflow: ellipsis;  
            overflow: hidden;
            display: inline-block;
          }
          .address{
            width:20%;
            color:#333333;
              font-size: 12px;
              float: right;
          }
        }
        .price-div {
          border-radius: 40px;
          height:45px;
          background:#FFEDED;
          width:220px;
          position:relative;
          .money{
            color:#F43B3B;
            font-size: 13px;
            line-height: 18px;
            font-weight: 500;
            margin-left: 20px;
            padding-top:3px;
            .small{
              margin-left:10px;
              font-size: 12px;
              text-decoration: line-through;
            }
          }
          img{
            position: absolute;
            right:0;
            top:0;
            width: 60px;
            height: 40px;
          }
        }
      }
    }
  }
</style>
