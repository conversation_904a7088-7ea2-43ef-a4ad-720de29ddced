<template>
  <div class="box flex">
    <div class="left flex">
      <div class="back flex" @click="handleGoBack">
        <img
          :src="`${VITE_API_IMG}/2024/08/d14d3009c5fa4c94a320a245187a8a03.png`"
          alt=""
          class="icon"
        />
        <div class="text">退出编辑</div>
      </div>
      <div class="text">正在装修：</div>
      <div class="text2 bold">{{ decorationInfo.name }}</div>
    </div>
    <div class="right">
      <a-dropdown>
        <template #overlay>
          <a-menu @click="handlePublish">
            <a-menu-item :key="1">立即发布</a-menu-item>
            <a-menu-item :key="2">定时发布</a-menu-item>
          </a-menu>
        </template>
        <a-button type="primary" :loading="publishDia.loading">
          发布
          <DownOutlined />
        </a-button>
      </a-dropdown>
    </div>
  </div>

  <!-- 定时发布-弹窗 -->
  <a-modal
    title="定时发布"
    width="34%"
    :visible="publishDia.visible"
    :close-on-overlay-click="false"
    prevent-scroll-through
    @cancel="closePublishDia"
  >
    <div class="pub-text">发布日期<span class="require">*</span></div>
    <a-date-picker
      v-model="publishDia.data.time"
      style="width: 100%"
      show-time
      allow-input
      :disable-date="{
        before: dayjs().subtract(1, 'day').format(),
        after: dayjs().add(30, 'day').format(),
      }"
      @change="onChangeTime"
    />
    <div class="pub-text2">可选择时间范围是当前时间5分钟后，以及30天内</div>
    <template #footer>
      <a-button @click="closePublishDia">取消</a-button>
      <a-button
        type="primary"
        :loading="publishDia.loading"
        @click="handleSchedulePublish"
        >发布</a-button
      >
    </template>
  </a-modal>

  <!-- 退出确认-弹窗 -->
  <a-modal
    :visible="goBackDia.visible"
    theme="danger"
    title="退出"
    @ok="confirmGoBack"
  >
    <div class="back-box">
      <div class="title">确定要退出吗？</div>
    </div>
    <template #footer>
      <a-button type="primary" @click="confirmGoBack">确定</a-button>
      <a-button @click="goBackDia.visible = false">取消</a-button>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
const { VITE_API_IMG } = import.meta.env;
import { storeToRefs } from "pinia";
import { reactive, ref } from "vue";
import dayjs from "dayjs";
import { message } from "woody-ui";
import { getDecorationStore } from "@/store";
import router from "@/router";
import { saveDecorationDetail } from "@/api/decoration";
import { validateDecoration } from "../config/validate";
import { DownOutlined } from "@ant-design/icons-vue";
const decorationStore = getDecorationStore();
const { decorationInfo } = storeToRefs(decorationStore);

const options = [
  { id: 1, content: "立即发布" },
  { id: 2, content: "定时发布" },
];
const publishDia = reactive({
  visible: false,
  loading: false,
  data: {
    time: "",
  },
});

const emits = defineEmits(["back"]);
// 满足后台需求，提交时候，修改商品组件，一行一个和一行两个的数据问题
const dataTreating = () => {
  debugger;
  decorationInfo.value.components.forEach((item) => {
    if (item.templateId === "goods") {
      if (item.info.style == 1) item.info.imgGroup = null;
    }
  });
  return decorationInfo.value;
};
const saveData = (schedulePublishTime: string) => {
  if (!decorationInfo.value.components.length) {
    message.error("请添加装修内容");
    return;
  }
  // console.log(validateDecoration(decorationInfo.value.components),'iscomponents')
  if (!validateDecoration(decorationInfo.value.components)) return;
  publishDia.loading = true;
  saveDecorationDetail({ ...dataTreating(), schedulePublishTime })
    .then((res) => {
      if (res.code === 0) {
        message.success("发布成功");
        publishDia.visible = false;
        publishDia.loading = false;
        setTimeout(() => {
          router.go(0);
        }, 500);
        //router.replace('/cms/index');
      }
    })
    .finally(() => {
      if (publishType.value === 1) {
        publishDia.visible = false;
      } else {
        // publishDia.visible = true;
      }
      publishDia.loading = false;
    });
};
const publishType = ref(1); // 1立即 2定时
const handlePublish = (data: any) => {
  console.log(data, "data123");
  if (data.key === 1) {
    publishType.value = 1;
    saveData("");
  } else {
    publishType.value = 2;
    openPublishDia();
  }
};
const openPublishDia = () => {
  publishDia.visible = true;
  publishDia.loading = false;
  publishDia.data.time = "";
};
const closePublishDia = () => {
  publishDia.visible = false;
};
const onChangeTime = (date) => {
  publishDia.data.time = dayjs(date).format("YYYY-MM-DD HH:mm:ss");
};
const handleSchedulePublish = () => {
  if (!publishDia.data.time) {
    message.error("请选择发布时间");
    return;
  }
  saveData(publishDia.data.time);
};

// 退出页面
const goBackDia = reactive({
  visible: false,
});
const handleGoBack = () => {
  goBackDia.visible = true;
};
const confirmGoBack = () => {
  goBackDia.visible = false;
  emits("back");
  // router.replace("/cms/index");
};
</script>

<style lang="scss" scoped>
.box {
  height: 60px;
  justify-content: space-between;
  padding: 0 32px;
  background-color: #fff;
  font-size: 14px;

  .left {
    .back {
      margin-right: 24px;
      cursor: pointer;

      .icon {
        width: 14px;
        height: 14px;
      }

      .text {
        color: #1d2426;
        margin-left: 4px;
      }
    }

    .info {
      color: #1d2426;
    }
  }
}
.pub-text {
  font-size: 14px;
  color: #05082c;
  margin-bottom: 8px;
}
.require {
  font-size: 14px;
  color: #ff436a;
  margin-left: 2px;
}
.pub-text2 {
  font-size: 12px;
  color: #636d7e;
  margin-top: 4px;
}
.back-box {
  margin: 16px;
  .title {
    font-weight: bold;
    font-size: 16px;
    color: #05082c;
    margin-bottom: 16px;
  }
  .sub-title {
    font-size: 14px;
    color: #495366;
    white-space: nowrap;
  }
}
</style>
