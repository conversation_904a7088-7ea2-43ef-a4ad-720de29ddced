<template>
  <div class="nav-flow-container">
    <div class="setting-group">
      <span class="label">支持滑动</span>
      <a-switch v-model:checked="navFlowData.sliding" :checked-value="1" :un-checked-value="0" />
      <p class="tip">当开启时，导航栏支持滑动</p>
    </div>

    <div class="setting-group">
      <span class="label">隐藏导航</span>
      <a-switch v-model:checked="navFlowData.onlyDisplayFirst" />
      <p class="tip">当隐藏导航时，仅展示第一个导航关联的内容</p>
    </div>


    <p class="list-tip">最多添加20个导航，支持拖动排序</p>

    <draggable :list="navFlowData.list" item-key="id" :animation="300" class="draggable-area" :move="checkMove">
      <template #item="{ element, index }">
        <div class="nav-item">
          <a-popconfirm
            v-if="index > 0"
            placement="left"
            title="确认删除吗"
            ok-text="确定"
            cancel-text="取消"
            @confirm="removeNavItem(index)"
          >
            <template #icon>
              <ExclamationCircleOutlined style="color: #faad14" />
            </template>
            <img
              class="delete-icon"
              :src="`${VITE_API_IMG}/2024/08/8e95e3dccdb743cc819885108a8ee29f.png`"
              alt="删除"
            />
          </a-popconfirm>
          <div class="item-content">
            <div class="img-upload-container">
              <span class="img-label">选中前 <span class="required">*</span></span>
              <div class="image-uploader" @click="openImageSelector(index, 'icon')">
                <img v-if="element.icon" :src="element.icon" class="preview-image" />
                <div v-else class="upload-placeholder">
                  <plus-outlined />
                  <span>上传图片</span>
                </div>
                <div v-if="element.icon" class="update-overlay">更换图片</div>
              </div>
            </div>
            <div class="img-upload-container">
              <span class="img-label">选中后 <span class="required">*</span></span>
              <div class="image-uploader" @click="openImageSelector(index, 'iconSelected')">
                <img v-if="element.iconSelected" :src="element.iconSelected" class="preview-image" />
                <div v-else class="upload-placeholder">
                  <plus-outlined />
                  <span>上传图片</span>
                </div>
                <div v-if="element.iconSelected" class="update-overlay">更换图片</div>
              </div>
            </div>
          </div>
          <p class="img-tip">图片尺寸不做限制。大小1M以内。</p>
          <div class="link-selector" @click="openLinkSelector(index)">
            <span class="link-label">链接</span>
            <span class="link-text" :class="{ placeholder: !element.uriName }">
              {{ element.uriName || "请选择链接" }}
            </span>
            <link-outlined class="arrow-icon" />
          </div>
        </div>
      </template>
    </draggable>

    <div class="add-nav-btn-wrapper">
      <a-button type="dashed" block @click="addNavItem" :disabled="navFlowData.list.length >= 20">
        + 添加导航 {{ navFlowData.list.length }}/20
      </a-button>
    </div>

    <SelectPage ref="selectPageRef" :enabled-tabs="currentEnabledTabs" @onPageCallBack="onLinkSelected" />
    <myPicture ref="myPictureRef" @on-image-call-back="onImageCallBack" />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, reactive, watchEffect } from 'vue';
  import draggable from 'vuedraggable';
  import { message } from 'woody-ui';
  import { RightOutlined, PlusOutlined, LinkOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { cloneDeep } from 'lodash-es';
  import SelectPage from './components/selectPage/index.vue';
  import myPicture from './components/myPicture.vue';
  import { getDecorationStore } from '@/store';
  import { storeToRefs } from 'pinia';
  import { createRelatedComponent, updateRelatedComponent, deleteRelatedComponent } from '@/services/relatedComponentService';

  const { VITE_API_IMG } = import.meta.env;

  const decorationStore = getDecorationStore();
  const { decorationInfo, activeNav } = storeToRefs(decorationStore);

  const navFlowData = ref<any>({});

  watchEffect(() => {
    const componentData = decorationInfo.value.components.find(
      (item: any) => item.flagId === activeNav.value.flagId
    );

    if (componentData) {
      if (!componentData.info) {
        componentData.info = {};
      }

      // 初始化默认值
      if (componentData.info.sliding === undefined) {
        componentData.info.sliding = 0;
      }
      if (componentData.info.onlyDisplayFirst === undefined) {
        componentData.info.onlyDisplayFirst = false;
      }
      if (!componentData.info.list) {
        componentData.info.list = [];
      }

      navFlowData.value = componentData.info;

      // 如果列表为空，添加默认导航项
      if (navFlowData.value.list.length === 0) {
        addDefaultNavItem();
      }
    }
  });
  
  const selectPageRef = ref(null);
  const myPictureRef = ref(null);
  const currentImageInfo = ref({ index: -1, type: '' });
  const currentLinkIndex = ref(0);
  const currentEnabledTabs = ref(null);
  
  // 定义允许的标签页
  const FIRST_NAV_TABS = [3, 4, 7]; // 首个导航只支持商品组件、品牌组件、系统内容页
  const OTHER_NAV_TABS = [3, 4, 5, 6, 7, 8]; // 其他导航支持商品组件、品牌组件、二级页面、系统页面、系统内容页、自定义链接

  function addDefaultNavItem() {
    if (navFlowData.value.list) {
      navFlowData.value.list.push({
        id: Date.now(),
        icon: '',
        iconSelected: '',
        uriName: null,
        uriType: null,
        param: null,
      });
    }
  }

  const addNavItem = () => {
    if (navFlowData.value.list.length >= 20) {
      message.warning('最多添加20个导航');
      return;
    }
    navFlowData.value.list.push({
      id: Date.now(),
      icon: '',
      iconSelected: '',
      uriName: null,
      uriType: null,
      param: null,
    });
  };

  const removeNavItem = (index: number) => {
    // 检查是否有关联组件需要删除
    const navItem = navFlowData.value.list[index];
    if (
      (navItem.uriType === 'GOODS_COMPONENT' || navItem.uriType === 'BRAND_COMPONENT') && 
      navItem.param && 
      navItem.param.componentId
    ) {
      // 删除关联组件
      deleteRelatedComponent(navItem.param.componentId);
    }
    
    // 删除导航项
    navFlowData.value.list.splice(index, 1);
  };
  
  const openImageSelector = (index, type) => {
    currentImageInfo.value = { index, type };
    myPictureRef.value?.showImageRef();
  };

  const onImageCallBack = ({ url }) => {
    const { index, type } = currentImageInfo.value;
    if (index > -1 && type && navFlowData.value.list[index]) {
      navFlowData.value.list[index][type] = url;
    }
  };

  const openLinkSelector = (index: number) => {
    currentLinkIndex.value = index;
    // 根据索引确定启用哪些标签页
    currentEnabledTabs.value = index === 0 ? FIRST_NAV_TABS : OTHER_NAV_TABS;
    selectPageRef.value?.selectPageRef(index);
  };

  const onLinkSelected = async (data: any) => {
    console.log(data,'选择数据data')
    if (navFlowData.value.list[currentLinkIndex.value]) {
      const currentItem = navFlowData.value.list[currentLinkIndex.value];
      const { enums, pageName, id, uri, name } = data;
      
      // 如果之前已经关联了商品组件或品牌组件，需要先删除旧的关联
      if (
        (currentItem.uriType === 'GOODS_COMPONENT' || currentItem.uriType === 'BRAND_COMPONENT') && 
        currentItem.param && 
        currentItem.param.componentId
      ) {
        deleteRelatedComponent(currentItem.param.componentId);
      }
      
      switch (enums) {
        case 'PAGE':
          // 二级页面选择（新版SelectPage组件返回的类型）
          currentItem.uriName = data.pageName || data.name;
          currentItem.uriType = 'PAGE';
          currentItem.param = { id: data.id };
          break;
        case 'GOODS':
          currentItem.uriName = data.prductName || data.productName;
          currentItem.uriType = 'GOODS';
          currentItem.param = { id: data.productId };
          break;
        case 'BRAND':
          currentItem.uriName = data.brandName;
          currentItem.uriType = 'BRAND';
          currentItem.param = { id: data.brandId };
          break;
        case 'GOODS_COMPONENT':
          currentItem.uriName = data.name || '商品组件';
          currentItem.uriType = 'GOODS_COMPONENT';
          
          // 创建关联的商品组件
          const goodsComponentId = await createRelatedComponent(
            activeNav.value.flagId,
            'GOODS_COMPONENT',
            {
              feCategoryId: data.feCategoryId,
              feCategoryName: data.feCategoryName,
              style: data.style || '2',
              imgGroup: data.imgGroup || []
            }
          );
          
          // 只保存关联组件ID
          currentItem.param = {
            componentId: goodsComponentId
          };
          break;
        case 'BRAND_COMPONENT':
          currentItem.uriName = data.name || '品牌组件';
          currentItem.uriType = 'BRAND_COMPONENT';
          
          // 创建关联的品牌组件
          const brandComponentId = await createRelatedComponent(
            activeNav.value.flagId,
            'BRAND_COMPONENT',
            {
              type: data.type,
              platCategoryId: data.platCategoryId,
              platCategoryName: data.platCategoryName,
              brands: data.brands || []
            }
          );
          
          // 只保存关联组件ID
          currentItem.param = {
            componentId: brandComponentId
          };
          break;
        case 'SHOP_COMPONENT':
          currentItem.uriName = data.name || '店铺组件';
          currentItem.uriType = 'SHOP_COMPONENT';
          currentItem.param = {
            type: data.type,
            platCategoryId: data.platCategoryId,
            platCategoryName: data.platCategoryName,
            brands: data.brands || []
          };
          break;
        case 'GROUPBUY_COMPONENT':
          currentItem.uriName = data.name || '团购组件';
          currentItem.uriType = 'GROUPBUY_COMPONENT';
          currentItem.param = {
            type: data.type,
            platCategoryId: data.platCategoryId,
            platCategoryName: data.platCategoryName,
            brands: data.brands || []
          };
          break;
        case 'SYSTEM_PAGE':
          currentItem.uriName = name || data.name;
          currentItem.uriType = 'SYSTEM_PAGE';
          currentItem.param = { id, uri };
          break;
        case 'SYSTEM_CONTENT_PAGE':
          currentItem.uriName = data.name;
          currentItem.uriType = 'SYSTEM_CONTENT_PAGE';
          currentItem.param = { id: data.contentId, uri: data.uri };
          break;
        case 'CUSTOM_LINK':
          currentItem.uriName = name || data.name || uri;
          currentItem.uriType = 'CUSTOM_LINK';
          currentItem.param = { uri };
          break;
        default:
          // 兼容旧版/其他类型
          currentItem.uriName = pageName || name;
          currentItem.uriType = enums;
          if (id) {
            currentItem.param = { id };
          }
          break;
      }
    }
  };

  // 检查拖动是否有效，防止第一个导航被替换
  const checkMove = (evt) => {
    // 如果是拖动到第一个位置，不允许
    if (evt.relatedContext.index === 0) {
      return false;
    }
    // 如果是从第一个位置拖动，不允许
    if (evt.draggedContext.index === 0) {
      return false;
    }
    return true;
  };

</script>

<style lang="less" scoped>
.nav-flow-container {
  padding: 16px;
  background-color: #ffffff;

  .setting-group {
    margin-bottom: 24px;

    .label {
      display: block;
      font-size: 14px;
      color: #333;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .tip {
      margin-top: 4px;
      font-size: 12px;
      color: #999;
    }
  }

  .list-tip {
    font-size: 12px;
    color: #999;
    margin-bottom: 12px;
  }

  .draggable-area {
    .nav-item {
      position: relative;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #e5e6eb;
      padding: 16px;
      margin-bottom: 12px;
      cursor: move;

      .delete-icon {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 16px;
        height: 16px;
        cursor: pointer;
        z-index: 10;
        visibility: hidden;
      }
      
      &:hover .delete-icon {
        visibility: visible;
      }
      
      .item-content {
        display: flex;
        gap: 20px;
        .img-upload-container {
          width: 140px;
          .img-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            .required {
              color: red;
              margin-left: 2px;
            }
          }
        }
      }

      .image-uploader {
        width: 100px;
        height: 100px;
        border: 1px dashed #d9d9d9;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        cursor: pointer;
        transition: border-color 0.3s;
        position: relative;
        overflow: hidden;

        &:hover {
          border-color: #1890ff;

          .update-overlay {
            visibility: visible;
            opacity: 1;
          }
        }

        .preview-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }

        .upload-placeholder {
          text-align: center;
          color: #999;
          
          span {
            font-size: 12px;
          }
        }

        .update-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 28px;
          line-height: 28px;
          background: rgba(0, 0, 0, 0.6);
          color: white;
          text-align: center;
          font-size: 12px;
          visibility: hidden;
          opacity: 0;
          transition: all 0.2s ease-in-out;
          border-bottom-left-radius: 4px;
          border-bottom-right-radius: 4px;
        }
      }

      .img-tip {
        color: #999;
        font-size: 12px;
        margin-top: 8px;
      }

      .link-selector {
        display: flex;
        align-items: center;
        margin-top: 16px;
        height: 36px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        cursor: pointer;
        background-color: #fff;
        padding: 0;
        transition: border-color 0.3s;

        &:hover {
          border-color: #1890ff;
        }

        .link-label {
          padding: 0 12px;
          height: 100%;
          display: flex;
          align-items: center;
          background-color: #fafafa;
          border-right: 1px solid #d9d9d9;
          font-size: 14px;
          color: #333;
        }

        .link-text {
          flex: 1;
          margin: 0;
          padding: 0 12px;
          color: #666;
          text-align: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          &.placeholder {
            color: #bfbfbf;
          }
        }
        .arrow-icon {
          font-size: 16px;
          color: #bfbfbf;
          padding-right: 12px;
        }
      }
    }
  }

  .add-nav-btn-wrapper {
    position: sticky;
    bottom: 0;
    background-color: #ffffff;
    padding: 16px 0;
    margin-top: 8px;
    z-index: 10;
  }
}
:deep(.ant-popover-buttons) {
  text-align: end;
  .ant-btn {
    margin-left: 8px;
  }
}
</style>
