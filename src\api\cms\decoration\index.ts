import request from "@/request";
import { Response } from "../../common";

const api = "/life-platform-dashboard";

// 所有素材分组查询
export const getGroupQuery = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `${api}/material/group/query?flatFlag=${params.flatFlag}`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 素材分组下所有素材数量查询
export const getGroupMateriallCnt = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `${api}/material/group/materialCnt?groupId=${params.groupId}`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 通过素材名称+分组进行列表搜索
export const getPageSearch = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/material/page-search`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 素材保存
export const getMaterialSave = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/material/save`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// page页面列表查询
export const basePage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/basePage/page?page=${params.current}&size=${params.pageSize}`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
// 查询商品列表
export const pageProduct = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/feCategoryProduct/pageProduct/v2?page=${params.current}&size=${params.pageSize}`,
      data: params,
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
// 查询品牌列表
export const brandInfo = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/akc/pageBrandInfo?page=${params.current}&size=${params.pageSize}`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
// 查询前台分类列表
export const feCategoryPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/feCategory/page?page=${params.page}&size=${params.size}`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
// 查询分类下面的商品
export const feCategorySearchPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/prod/feCategory/search/page?page=${params.page}&size=${params.size}`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
// 查询指定分类列表
export const akcPageCategory = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/akc/pageCategory?page=${params.current}&size=${params.pageSize}`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
// 查询分类列表
export const akcListCategory = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/akc/listCategory`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
// 查询品牌分类列表
export const akcPageBrandList = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/akc/pageBrandList?page=${params.page}&size=${params.size}`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
// 查询商品集合列表
export const akcPageBrandInfo = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/akc/pageBrandInfo?page=${params.current}&size=${params.pageSize}`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

// 获取系统页面列表
export const getSystemPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "GET",
      path: `${api}/systemPage/listSystemPage?page=${params.current}&size=${params.pageSize}`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};

//支付页列表查询

export const queryFixedPage = (params: any) => {
  try {
    return request<Response<any>>({
      method: "POST",
      path: `${api}/fixedPage/page?page=${params.current}&size=${params.size}`,
      data: params,
      headers: { "X-Accept-Version": "wx1" },
    });
  } catch (error) {
    // 处理请求失败的情况
    console.error("Request failed:", error);
    throw error;
  }
};
