<template>
  <div class="king-kong-container">
    <div class="title">金刚位</div>

    <div class="setting-group">
      <span class="label">展示数量</span>
      <a-radio-group v-model:value="jinGangData.displayCount">
        <a-radio :value="5">一页5个</a-radio>
        <a-radio :value="10">一页10个</a-radio>
      </a-radio-group>
    </div>

    <div class="add-image-section">
      <span class="section-label">添加图片</span>
      <span class="section-desc">最多添加50个，支持拖动排序</span>
    </div>
    <p class="tip">建议图片尺寸宽度710px，高度710px，大小1M以内。</p>

    <draggable :list="jinGangData.list" item-key="id" :animation="300" class="draggable-area">
      <template #item="{ element, index }">
        <div class="item-card">
          <a-popconfirm
            v-if="jinGangData.list.length > 1"
            placement="left"
            title="确认删除吗"
            ok-text="确定"
            cancel-text="取消"
            @confirm="removeItem(index)"
          >
            <template #icon>
              <ExclamationCircleOutlined style="color: #faad14" />
            </template>
            <img
              class="delete-icon"
              :src="`${VITE_API_IMG}/2024/08/8e95e3dccdb743cc819885108a8ee29f.png`"
              alt="删除"
            />
          </a-popconfirm>

          <div class="image-uploader" @click="openImageSelector(index)">
            <img v-if="element.imgUrl" :src="element.imgUrl" class="preview-image" />
            <div v-else class="upload-placeholder">
              <plus-outlined />
              <span>点击上传图片</span>
            </div>
            <div v-if="element.imgUrl" class="update-overlay">更换图片</div>
          </div>

          <div class="inputs-container">
            <div class="link-selector">
              <span class="link-label">名称</span>
              <a-input v-model:value="element.name" placeholder="请输入名称" class="input-value" :maxlength="4" />
            </div>
            <div class="link-selector" @click="openLinkSelector(index)">
              <span class="link-label">链接</span>
              <span class="link-text" :class="{ placeholder: !element.uriName }">
                {{ element.uriName || "请选择链接" }}
              </span>
              <link-outlined class="arrow-icon" />
            </div>
          </div>
        </div>
      </template>
    </draggable>

    <div class="add-nav-btn-wrapper">
      <a-button type="dashed" block @click="addItem" :disabled="jinGangData.list.length >= 50">
        + 添加导航 {{ jinGangData.list.length }}/50
      </a-button>
    </div>

    <SelectPage ref="selectPageRef" :enabled-tabs="[5, 6, 8]" @onPageCallBack="onLinkSelected" />
    <myPicture ref="myPictureRef" @on-image-call-back="onImageCallBack" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, watchEffect } from 'vue';
import draggable from 'vuedraggable';
import { message } from 'woody-ui';
import { PlusOutlined, LinkOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { cloneDeep } from 'lodash-es';
import SelectPage from './components/selectPage/index.vue';
import myPicture from './components/myPicture.vue';
import { getDecorationStore } from '@/store';
import { storeToRefs } from 'pinia';

const { VITE_API_IMG } = import.meta.env;

const decorationStore = getDecorationStore();
const { decorationInfo, activeNav } = storeToRefs(decorationStore);

const jinGangData = ref<any>({});

watchEffect(() => {
  const componentData = decorationInfo.value.components.find(
    (item: any) => item.flagId === activeNav.value.flagId
  );

  if (componentData) {
    if (!componentData.info) {
      componentData.info = {};
    }

    if (componentData.info.displayCount === undefined) {
      componentData.info.displayCount = 10;
    }
    if (!componentData.info.list) {
      componentData.info.list = [];
    }

    jinGangData.value = componentData.info;

    if (jinGangData.value.list.length === 0) {
      addItem();
    }
  }
});

const selectPageRef = ref(null);
const myPictureRef = ref(null);
const currentImageInfo = ref({ index: -1 });
const currentLinkIndex = ref(0);

function addItem() {
  if (jinGangData.value.list.length >= 50) {
    message.warning('最多添加50个导航');
    return;
  }
  jinGangData.value.list.push({
    id: Date.now(),
    imgUrl: '',
    name: '',
    uriName: null,
    uriType: null,
    param: null,
  });
}

function removeItem(index: number) {
  jinGangData.value.list.splice(index, 1);
}

function openImageSelector(index: number) {
  currentImageInfo.value = { index };
  myPictureRef.value?.showImageRef();
}

function onImageCallBack({ url }) {
  const { index } = currentImageInfo.value;
  if (index > -1 && jinGangData.value.list[index]) {
    jinGangData.value.list[index].imgUrl = url;
  }
}

function openLinkSelector(index: number) {
  currentLinkIndex.value = index;
  selectPageRef.value?.selectPageRef(index);
}

function onLinkSelected(data: any) {
  const { enums, pageName, id, uri, name } = data;
  const currentItem = jinGangData.value.list[currentLinkIndex.value];
  if (!currentItem) return;
  switch (enums) {
    case 'PAGE':
      currentItem.uriName = pageName;
      currentItem.param = { id };
      break;
    case 'SYSTEM_PAGE':
      currentItem.uriName = name;
      currentItem.param = { id, uri };
      break;
    case 'CUSTOM_LINK':
      currentItem.uriName = name;
      currentItem.param = { uri };
      break;
    default:
      Object.assign(currentItem, {
        uriName: data.uriName,
        uriType: data.uriType,
        param: data.param,
      });
      break;
  }
}
</script>

<style lang="less" scoped>
.king-kong-container {
  padding: 16px;
  background-color: #ffffff;

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 24px;
  }

  .setting-group {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .label {
      font-size: 14px;
      color: #333;
      margin-right: 16px;
    }
  }

  .add-image-section {
    margin-bottom: 8px;
    .section-label {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
    .section-desc {
      font-size: 12px;
      color: #999;
      margin-left: 8px;
    }
  }
  .tip {
    font-size: 12px;
    color: #999;
    margin-bottom: 16px;
  }

  .draggable-area {
    .item-card {
      position: relative;
      display: flex;
      gap: 16px;
      align-items: flex-start;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #e5e6eb;
      padding: 16px;
      margin-bottom: 12px;
      cursor: move;

      .delete-icon {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 16px;
        height: 16px;
        cursor: pointer;
        z-index: 10;
        visibility: hidden;
      }
      
      &:hover .delete-icon {
        visibility: visible;
      }

      .image-uploader {
        width: 100px;
        height: 100px;
        flex-shrink: 0;
        border: 1px dashed #d9d9d9;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        cursor: pointer;
        transition: border-color 0.3s;
        position: relative;
        overflow: hidden;

        &:hover {
          border-color: #1890ff;
          .update-overlay {
            visibility: visible;
            opacity: 1;
          }
        }

        .preview-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }

        .upload-placeholder {
          text-align: center;
          color: #999;
          
          span {
            font-size: 12px;
          }
        }

        .update-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 28px;
          line-height: 28px;
          background: rgba(0, 0, 0, 0.6);
          color: white;
          text-align: center;
          font-size: 12px;
          visibility: hidden;
          opacity: 0;
          transition: all 0.2s ease-in-out;
        }
      }

      .inputs-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 16px;
        min-width: 0;
      }

      .link-selector {
        display: flex;
        align-items: center;
        height: 36px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fff;
        padding: 0;
        transition: border-color 0.3s;

        &:hover {
          border-color: #1890ff;
        }

        .link-label {
          padding: 0 12px;
          height: 100%;
          display: flex;
          align-items: center;
          background-color: #fafafa;
          border-right: 1px solid #d9d9d9;
          font-size: 14px;
          color: #333;
          white-space: nowrap;
        }

        .link-text {
          flex: 1;
          padding: 0 12px;
          color: #666;
          text-align: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          &.placeholder {
            color: #bfbfbf;
          }
        }

        .input-value {
          flex: 1;
          border: none;
          box-shadow: none;
          height: 100%;
          padding: 0 12px;
          min-width: 0;
        }

        .arrow-icon {
          font-size: 16px;
          color: #bfbfbf;
          margin-right: 12px;
        }
      }
    }
  }

  .add-nav-btn-wrapper {
    position: sticky;
    bottom: 0;
    background-color: #ffffff;
    padding: 16px 0;
    margin-top: 8px;
    z-index: 10;
  }
}

:deep(.ant-popover-buttons) {
  text-align: end;
  .ant-btn {
    margin-left: 8px;
  }
}
</style>
