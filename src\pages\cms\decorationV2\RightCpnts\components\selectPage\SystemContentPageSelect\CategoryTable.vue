<template>
  <div class="goodsmam-table">
    <a-table
      :row-selection="rowSelection"
      row-key="key"
      :columns="columns"
      :data-source="tableData"
      :loading="isLoading"
      :expand-row-by-click="true"
      :pagination="false"
      :row-class-name="rowClassName"
    ></a-table>
  </div>
</template>
<script setup lang="ts">
  import { ref, computed } from 'vue';

  const props = defineProps({
    tableData: {
      type: Array,
      default: () => [],
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'SHOP_COMPONENT',
    },
  });

  const emit = defineEmits(['onSelect']);
  const selectedRowKeys = ref<any[]>([]);
  const selectedRow = ref<any>(null);

  const onSelectChange = (keys: any[], selectedRows: any[]) => {
    if (selectedRows.length > 0) {
      const record = selectedRows[0];
      selectedRowKeys.value = keys;
      selectedRow.value = record;
      emit('onSelect', {
        enums: props.type,
        name: record.title,
        uri: record.uri,
      });
    }
  };

  const rowSelection = computed(() => {
    return {
      type: 'radio',
      selectedRowKeys: selectedRowKeys.value,
      onChange: onSelectChange,
    };
  });

  const columns = [
    {
      title: '分类名称',
      dataIndex: 'title',
      key: 'title',
      align: 'left',
    },
  ];

  const rowClassName = (record: any) => {
    const classNames = [];
    if (record.children && record.children.length > 0) {
      // 可选：如果需要，为父行添加class
    } else {
      classNames.push('child-row');
    }
    if (selectedRow.value && record.key === selectedRow.value.key) {
      classNames.push('selected-row');
    }
    return classNames.join(' ');
  };

  const resetData = () => {
    selectedRow.value = null;
    selectedRowKeys.value = [];
  };

  const clearSelection = () => {
    selectedRow.value = null;
    selectedRowKeys.value = [];
  };

  defineExpose({ resetData, clearSelection });
</script>
<style lang="less" scoped>
  .goodsmam-table {
    margin-top: 16px;
  }

  .title-cell-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  :deep(.child-row .ant-table-row-indent) {
    /* 根据需要调整子节点的缩进 */
  }

  :deep(.selected-row > td),
  :deep(.selected-row:hover > td) {
    background-color: #e6f4ff !important;
  }
</style>
