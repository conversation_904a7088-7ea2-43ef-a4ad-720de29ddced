<template>
  <div class="adver-content">
    <templat v-if="sceneType != 'goods' && sceneType != 'brand'">
      <div class="advert-title">背景颜色渐变轮播</div>
      <div class="advert-switch">
        <a-switch v-model:checked="advertData.bkChange" size="medium" />
      </div>
    </templat>
    <div class="advert-image">
      <span class="advert-image-one">添加图片</span>
      <span class="advert-image-two">支持拖动排序</span>
    </div>
    <template v-if="sceneType === 'firstScreen'">
      <div class="advert-image-cd">建议图片尺寸宽度710px，高度240px。</div>
    </template>
    <template v-if="sceneType !== 'firstScreen'">
      <div class="advert-image-cd"></div>
    </template>
    <div class="advert-add-border-list">
      <draggable :sort="true" :list="advertData.list" :animation="300">
        <template #item="{ element, index }">
          <div class="advert-add-border">
            <div class="advert-add-border-pad">
              <div
                class="advert-addImage-left"
                @click="showImagePopupMethod(index)"
              >
                <template v-if="element.imgUrl">
                  <img class="advert-addImage-left-img" :src="element.imgUrl" />
                </template>
                <template v-if="!element.imgUrl">
                  <img
                    :src="`${VITE_API_IMG}/2024/08/061bfc84196541b9b8210e5f0ee43fc9.png`"
                  />
                  <p>点击上传图片</p>
                </template>
                <div v-if="element.imgUrl" class="aduert-update">更换图片</div>
              </div>
              <div class="advert-addImage-right">
                <template v-if="sceneType !== 'firstScreen'">
                  <div class="advert-addImage-right-cd">
                    建议图片尺寸宽度710px，高度不限制。大小1M以内。
                  </div>
                </template>
                <div
                  class="advert-addImage-right-Input"
                  @click="showUrlPopupMethod(index)"
                >
                  <template v-if="!element.uriName && !element.param.id">
                    <div class="right-Input-tz">链接</div>
                    <div class="right-Input-text">请选择链接</div>
                  </template>
                  <template
                    v-if="element.uriName != null && element.param.id != null"
                  >
                    <div class="right-Input-mm">{{ element.uriName }}</div>
                  </template>
                  <img
                    class="right-Input-img"
                    :src="`${VITE_API_IMG}/2024/08/7c67138a798d4365a216e8f2b9235fd4.png`"
                  />
                </div>
                <template v-if="sceneType != 'goods' && sceneType != 'brand'">
                  <template v-if="advertData.bkChange">
                    <div class="advert-addImage-right-bg">
                      <p>背景色设置</p>
                      <color-picker v-model:pureColor="element.bgColor" />
                    </div>
                  </template>
                </template>
                <div class="display-rule-settings" @click="showDisplayRuleModal(element, index)">
                    <!-- <img :src="`${VITE_API_IMG}/2024/08/gear-icon.png`" class="settings-icon" /> -->
                    <SettingOutlined class="settings-icon"/>
                    <span>展示规则设置</span>
                </div>
              </div>
            </div>
            <a-popconfirm title="确认删除吗" @confirm="shutImage(index)">
              <img
                class="brand-shut-Img"
                :src="`${VITE_API_IMG}/2024/08/b48cb30452984634ba1c6114202be146.png`"
              />
            </a-popconfirm>
          </div>
        </template>
      </draggable>
    </div>
    <div class="advert-addImage-pus" @click="addImage">
      <div class="advert-addImage-text">
        <img
          :src="`${VITE_API_IMG}/2024/08/63efdd3174694b03964624fa31b724b6.png`"
        />
        <div class="advert-addImage-tp">
          <span>添加图片{{ advertData.list.length || "0" }}/</span>
          <span>10</span>
        </div>
      </div>
    </div>
  </div>
  <!-- 我的图片列表弹框 -->
  <myPicture ref="myPictureRef" @on-image-call-back="onImageCallBack" />
  <!-- 选择跳转页面（内容）弹框 -->
  <selectPage ref="selectPageRef" :enabledTabs="[1, 2, 5, 6, 8]" @on-page-call-back="onPageCallBack" />
  <!-- 展示规则设置弹框 -->
  <DisplayRuleModal ref="displayRuleModalRef" @confirm="onDisplayRuleConfirm" />
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import draggable from "vuedraggable";
import { storeToRefs } from "pinia";
import { getDecorationStore } from "@/store";
import { ImageData } from "../type";
import myPicture from "./components/myPicture.vue";
import selectPage from "./components/selectPage/index.vue";
import DisplayRuleModal from "./components/DisplayRuleModal.vue";
import { SettingOutlined } from "@ant-design/icons-vue";
const { VITE_API_IMG } = import.meta.env;

const decorationStore = getDecorationStore();
const { decorationInfo } = storeToRefs(decorationStore);
const sceneType = decorationInfo.value.scene;
const detailData = decorationInfo.value.components.find(
  (item: any) => item.templateId === "advert"
);
const advertData = ref<any>({});
advertData.value = detailData.info;
type myPictureType = { showImageRef: (index: number) => void };
const myPictureRef = ref<myPictureType | null>(null);
type selectPageType = { selectPageRef: (index) => void };
const selectPageRef = ref<selectPageType | null>(null);
const displayRuleModalRef = ref<any | null>(null);

const addImage = () => {
  if (advertData.value.list.length >= 10) return;
  advertData.value.list.push({
    imgUrl: null,
    bgColor: "#FFFFFF",
    uriType: 0,
    uriName: null,
    param: { id: null },
    timerControl: {
      cycleType: "longTerm",
      loopFlag: 0,
      timer: null,
    },
  } as ImageData);
};
const shutImage = (index: number) => {
  advertData.value.list.splice(index, 1);
};
const onPageCallBack = (item) => {
  const {
    index,
    id,
    prodSource,
    productId,
    pageName,
    brandName,
    prductName,
    brandId,
    enums,
  } = item;
  switch (enums) {
    case "PAGE":
      advertData.value.list[index].uriType = 0;
      advertData.value.list[index].uriName = pageName;
      advertData.value.list[index].param.id = id;
      break;
    case "GOODS":
      advertData.value.list[index].uriType = 1;
      advertData.value.list[index].uriName = prductName;
      advertData.value.list[index].param.id = productId;
      advertData.value.list[index].param.prodSource = prodSource;
      break;
    case "BRAND":
      advertData.value.list[index].uriType = 2;
      advertData.value.list[index].uriName = brandName;
      advertData.value.list[index].param.id = brandId;
      break;
  }
};
const onImageCallBack = (item: { index: number; url: string }) => {
  const { index, url } = item;
  advertData.value.list[index].imgUrl = url;
};
const showImagePopupMethod = (index: number) => {
  if (myPictureRef.value) {
    if (typeof myPictureRef.value.showImageRef === "function") {
      myPictureRef.value.showImageRef(index);
    }
  }
};
const showUrlPopupMethod = (index: number) => {
  if (selectPageRef.value) {
    if (typeof selectPageRef.value.selectPageRef === "function") {
      selectPageRef.value.selectPageRef(index);
    }
  }
};

const showDisplayRuleModal = (item, index) => {
    if(displayRuleModalRef.value) {
        displayRuleModalRef.value.openModal(item, index);
    }
}

const onDisplayRuleConfirm = (rules, index) => {
    if (advertData.value.list[index]) {
        advertData.value.list[index].timerControl = rules;
    }
}

onMounted(() => {});
</script>
<script lang="ts">
export default {
  name: "advert",
};
</script>
<style lang="less" scoped>
@import "../css/advert.less";

.display-rule-settings {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #1A7AF8;
    margin-top: 10px;

    .settings-icon {
        width: 14px;
        height: 14px;
        margin-right: 5px;
    }
}
</style>
