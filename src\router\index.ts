import { createRouter, createWebHashHistory, RouteRecordRaw } from "vue-router";
import uniq from "lodash/uniq";

// 自动导入modules文件夹下所有ts文件
const modules = import.meta.globEager("./modules/**/*.ts");
// 路由暂存
const routeModuleList: Array<RouteRecordRaw> = [];
Object.keys(modules).forEach((key) => {
  const mod = modules[key].default || {};
  const modList = Array.isArray(mod) ? [...mod] : [mod];
  routeModuleList.push(...modList);
});

// 关于单层路由，meta 中设置 { single: true } 即可为单层路由，{ hidden: true } 即可在侧边栏隐藏该路由
// 存放动态路由
export const asyncRouterList: Array<RouteRecordRaw> = [...routeModuleList];

// 存放固定的路由
const defaultRouterList: Array<RouteRecordRaw> = [
  {
    path: "/login",
    name: "login",
    component: () => import("@/pages/login/index.vue"),
  },
  {
    path: "/cms/decoration/:id",
    name: "decoration",
    component: () => import("@/pages/cms/decoration/index.vue"),
  },
  {
    path: "/cms/decorationV2/:id",
    name: "decorationV2",
    component: () => import("@/pages/cms/decorationV2/index.vue"),
  },
  {
    path: "/cms/payDecoration/:id",
    name: "payDecoration",
    component: () => import("@/pages/cms/fixedConfig/detail.vue"),
  },
  {
    path: "/",
    redirect: "/product",
  },
  {
    path: "/:w+",
    name: "404Page",
    redirect: "/result/404",
  },
];

export const allRoutes = [...defaultRouterList, ...asyncRouterList];

export const getRoutesExpanded = () => {
  const expandedRoutes = [];
  allRoutes.forEach((item) => {
    if (item.meta && item.meta.expanded) {
      expandedRoutes.push(item.path);
    }
    if (item.children && item.children.length > 0) {
      item.children
        .filter((child) => child.meta && child.meta.expanded)
        .forEach((child: RouteRecordRaw) => {
          expandedRoutes.push(item.path);
          expandedRoutes.push(`${item.path}/${child.path}`);
        });
    }
  });
  return uniq(expandedRoutes);
};

export const getActive = (maxLevel: number, route: any): string => {
  if (!route || !route.path) {
    return "";
  }
  return route.path
    .split("/")
    .filter((_item: string, index: number) => index <= maxLevel && index > 0)
    .map((item: string) => `/${item}`)
    .join("");
};

const router = createRouter({
  history: createWebHashHistory(),
  routes: allRoutes,
  scrollBehavior() {
    return {
      el: "#app",
      top: 0,
      behavior: "smooth",
    };
  },
});

export default router;
