<template>
  <div class="custom-link-select">
    <div class="input-container">
      <div class="input-label">移动端链接</div>
      <a-input
        v-model:value="linkValue"
        placeholder="请输入h5/小程序/App链接"
        clearable
      />
    </div>
    <div class="tip-container">
      <div class="tip-icon">
        <span class="icon-symbol">ⓘ</span>
        提示
      </div>
      <div class="tip-text">
        您可以输入任意有效的链接地址，例如:https://xxx.xxx.xxx注意事项:在移动端配置外部链接时，请确保已在相应的小程序平台上添加该域名至白名单，否则链接可能无法正常访问
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref } from 'vue';

  const emit = defineEmits(['onFormData']);
  
  const linkValue = ref('');
  
  // 暴露方法给父组件
  defineExpose({
    resetData: () => {
      linkValue.value = '';
    }
  });

  // 监听输入变化，实时更新到父组件
  const updateLink = () => {
    if (linkValue.value) {
      emit('onFormData', {
        uriName: '自定义链接',
        uriType: 'CUSTOM',
        uriValue: linkValue.value,
        enums: 'CUSTOM_LINK'
      });
    }
  };

  // 监听输入变化
  watch(linkValue, () => {
    updateLink();
  });
</script>
<script lang="ts">
import { watch } from 'vue';

export default {
  name: 'CustomLinkSelect'
}
</script>
<style lang="less" scoped>
  .custom-link-select {
    padding: 20px;
    
    .input-container {
      margin-bottom: 20px;
      
      .input-label {
        font-size: 14px;
        color: #333;
        margin-bottom: 8px;
      }
    }
    
    .tip-container {
      display: flex;
      flex-direction: column;
      background-color: #F7FAFF;
      padding: 8px;
      border-radius: 4px;
      
      .tip-icon {
        display: flex;
        align-items: center;
        color: #818999;
        font-size: 14px;
        margin-bottom: 8px;

        .icon-symbol {
          color: #636D7E;
          margin-right: 4px;
          font-size: 18px;
          font-weight: normal;
        }
      }
      
      .tip-text {
        font-size: 12px;
        color: #666;
        line-height: 1.5;
      }
    }
  }
</style> 