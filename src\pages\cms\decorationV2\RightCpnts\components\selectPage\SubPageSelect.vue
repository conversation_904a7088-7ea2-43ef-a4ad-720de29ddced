<template>
  <div class="page-select">
    <div class="page-select-size">
      <a-form ref="pageRef" layout="vertical" :model="pageData">
        <a-row :gutter="20">
          <a-col :span="8">
            <a-form-item label="标题">
              <a-input v-model:value="pageData.pageName" placeholder="请输入内容" clearable />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item>
              <div style="margin-top: 28px">
                <a-button type="primary" @click="pageInquire" class="mr10">查询</a-button>
                <a-button @click="resetPageOk">重置</a-button>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="page-select-table">
      <a-table
        min-width="2000"
        max-height="500px"
        :columns="pageColumns"
        :pagination="pageSizes"
        :data-source="pageTabelData"
        :loading="isPageLoading"
        :row-selection="rowSelection"
        row-key="id"
        hover
        @change="onPageSize"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'radio'">
            <a-radio
              :checked="selectedRowKeys?.id === record.id"
              @change="() => pageSelect(record)"
            />
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'woody-ui';
  import { PAGE_COLUMNS, PAGE_TYPE, PAGESIZE } from '../const';
  import { basePage } from '@/api/cms/decoration/index';

  const emit = defineEmits(['onFormData']);
  
  // table页面数据列表
  const pageRef = ref(null);
  const pageColumns = reactive(PAGE_COLUMNS);
  const pageData = reactive({ ...PAGE_TYPE });
  const pageSizes = reactive({ ...PAGESIZE });
  const pageTabelData = ref<any[]>([]);
  const isPageLoading = ref<boolean>(false);
  const selectedRowKeys = ref(null);
  
  // 行选择配置（设置为null禁用默认的多选）
  const rowSelection = null;

  // 获取页面列表
  const httpBasePage = async () => {
    isPageLoading.value = true;
    try {
      const { current, pageSize } = pageSizes;
      const params = { ...pageData, current, pageSize };
      const res = await basePage(params);
      pageTabelData.value = res.data.records;
      pageSizes.total = res.data.total;
    } catch (error) {
      pageTabelData.value = [];
      pageSizes.total = 0;
    } finally {
      isPageLoading.value = false;
    }
  };

  // 页面分页
  const onPageSize = event => {
    pageSizes.current = event.current;
    pageSizes.pageSize = event.pageSize;
    httpBasePage();
  };

  // 页面查询
  const pageInquire = () => {
    pageSizes.current = 1;
    pageSizes.pageSize = 10;
    selectedRowKeys.value = null;
    httpBasePage();
  };

  // 页面重置
  const resetPageOk = () => {
    pageSizes.current = 1;
    pageSizes.pageSize = 10;
    pageData.pageName = null;
    selectedRowKeys.value = null;
    httpBasePage();
  };

  // 获取页面ID
  const pageSelect = record => {
    selectedRowKeys.value = record;
    emit('onFormData', { ...record, enums: 'PAGE' });
  };

  onMounted(() => {
    httpBasePage();
  });

  // 暴露方法给父组件
  defineExpose({ 
    resetData: () => {
      pageSizes.current = 1;
      pageData.pageName = null;
      pageTabelData.value = [];
      selectedRowKeys.value = null;
      httpBasePage();
    }
  });
</script>
<script lang="ts">
export default {
  name: 'SubPageSelect'
}
</script>
<style lang="less" scoped>
  .page-select {
    .page-select-size {
      padding-top: 48px;
    }

    .page-select-table {
      padding-top: 24px;
    }
  }
  :deep(th) {
    background-color: @table-th-bg !important;
    color: @table-th-color;
    font-weight: 400;
    &:first-child {
      border-top-left-radius: 8px;
    }
    &:last-child {
      border-top-right-radius: 8px;
    }
  }
  :deep(td) {
    border-bottom-color: @table-boder-color;
  }
  :deep(.t-table__pagination) {
    padding-top: 28px;
    .t-pagination {
      .t-pagination__jump {
        margin-left: 16px;
      }
    }
  }
</style> 