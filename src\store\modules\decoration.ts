import { defineStore } from 'pinia';
import { store } from '@/store';
import { DecorationInfo } from '@/pages/cms/decoration/type';
import { moveElementToFront, moveElementForward, moveElementBackward, moveElementToEnd } from '@/utils/decoration';

export const useDecorationStore = defineStore('decoration', {
  state: () => ({
    decorationInfo: {} as DecorationInfo,
    activeNav: { templateId: null, flagId: '' },
  }),
  actions: {
    handleReset() {
      this.decorationInfo = {} as DecorationInfo;
      this.activeNav = { templateId: null, flagId: '' };
    },
    setDecorationInfo(decorationInfo: DecorationInfo) {
      this.decorationInfo = decorationInfo;
    },
    setActiveNav(activeNav: any) {
      this.activeNav = activeNav;
    },
    setNavSort(type: number) {
      const { components } = this.decorationInfo;
      const tempObj = { activeNavIndex: 0, operatioApi: '' };
      components.forEach((item: any, index: number) => {
        if (item.flagId === this.activeNav.flagId) {
          tempObj.activeNavIndex = index;
        }
      });
      if (type === 1) {
        this.decorationInfo.components = moveElementToFront(components, tempObj.activeNavIndex);
      } else if (type === 2) {
        this.decorationInfo.components = moveElementForward(components, tempObj.activeNavIndex);
      } else if (type === 3) {
        this.decorationInfo.components = moveElementBackward(components, tempObj.activeNavIndex);
      } else if (type === 4) {
        this.decorationInfo.components = moveElementToEnd(components, tempObj.activeNavIndex);
      }
    },
    addRelatedComponent(component: any) {
      if (!this.decorationInfo.relatedComponents) {
        this.decorationInfo.relatedComponents = [];
      }
      this.decorationInfo.relatedComponents.push(component);
    },
    updateRelatedComponent(componentId: string | number, componentData: any) {
      if (!this.decorationInfo.relatedComponents) {
        return;
      }
      
      const index = this.decorationInfo.relatedComponents.findIndex(
        (item: any) => String(item.id) === String(componentId)
      );
      
      if (index !== -1) {
        this.decorationInfo.relatedComponents[index] = {
          ...this.decorationInfo.relatedComponents[index],
          ...componentData
        };
      }
    },
    deleteRelatedComponent(componentId: string | number) {
      if (!this.decorationInfo.relatedComponents) {
        return;
      }
      
      this.decorationInfo.relatedComponents = this.decorationInfo.relatedComponents.filter(
        (item: any) => String(item.id) !== String(componentId)
      );
    }
  },
});

export function getDecorationStore() {
  return useDecorationStore(store);
}
