import { message } from "woody-ui";
import { getDecorationStore } from "@/store";

const decorationStore = getDecorationStore();

interface ValidateResult {
  templateId: string;
  flagId: string;
  messages: string;
}

function validateDecoration(components: any) {
  const result = getResultByRules(components);
  const { templateId, flagId, messages } = result;
  let isValid = false;
  if (messages) {
    message.error(messages);
    decorationStore.setActiveNav({ templateId, flagId });
    isValid = false;
  } else {
    isValid = true;
  }
  return isValid;
}

function getResultByRules(components: any) {
  const result: ValidateResult = { templateId: "", flagId: "", messages: "" };
  for (const item of components) {
    const { templateId, flagId } = item;
    const info = item.info ? item.info : {};
    if (templateId === "subPageTitle") {
      if (!info.text) {
        result.messages = "请输入标题";
        result.flagId = flagId;
        result.templateId = templateId;
        break;
      }
    }
    if (templateId === "search") {
      if (!info.id) {
        result.messages = "请选择搜索页面";
        result.flagId = flagId;
        result.templateId = templateId;
        break;
      }
    }
    if (templateId === "contentSearch" && info.id !== null && !info.id) {
      result.messages = "请选择搜索页面";
      result.flagId = flagId;
      result.templateId = templateId;
      break;
    }
    if (templateId === "content") {
      if (!info.id) {
        result.messages = "请选择内容";
        result.flagId = flagId;
        result.templateId = templateId;
        break;
      }
    }
    if (templateId === "notice") {
      if (!info.text) {
        result.messages = "请输入公告内容";
        result.flagId = flagId;
        result.templateId = templateId;
        break;
      }
    }
    if (templateId === "homePageTitle") {
      if (!info.bgColor) {
        result.messages = "请选择标题栏背景色";
        result.flagId = flagId;
        result.templateId = templateId;
        break;
      }
      if (!info.searchPageId) {
        result.messages = "请选择搜索页面";
        result.flagId = flagId;
        result.templateId = templateId;
        break;
      }
      if (info.isSearchSwiperWords && !info.rollingWordsId) {
        result.messages = "请选择滚动词组";
        result.flagId = flagId;
        result.templateId = templateId;
        break;
      }
    }
    if (templateId === "navSingle") {
      if (!info.bgColor) {
        result.messages = "请选择导航栏背景色";
        result.flagId = flagId;
        result.templateId = templateId;
        break;
      }
      for (const item of info.list) {
        if (!item.name) {
          result.messages = "请将导航名称填写完整";
          result.flagId = flagId;
          result.templateId = templateId;
          break;
        }
        if (!item?.param?.id) {
          result.messages = "请将导航链接填写完整";
          result.flagId = flagId;
          result.templateId = templateId;
          break;
        }
      }
    }
    if (templateId === "advert") {
      for (const item of info.list) {
        if (!item.imgUrl) {
          result.messages = "轮播广告-请点击上传图片";
          result.flagId = flagId;
          result.templateId = templateId;
          break;
        }
      }
    }
    if (templateId === "singleAdvert") {
      if (!info.imgUrl) {
        result.messages = "单图广告-请点击上传图片";
        result.flagId = flagId;
        result.templateId = templateId;
        break;
      }
    }
    if (templateId === "cube") {
      for (const item of info.list) {
        if (!item.imgUrl) {
          result.messages = "魔方-请点击上传图片";
          result.flagId = flagId;
          result.templateId = templateId;
          break;
        }
      }
    }
    if (templateId === "navLinkage") {
      for (const item of info.list) {
        if (!item.name) {
          result.messages = "联动导航-请填写一级标题";
          result.flagId = flagId;
          result.templateId = templateId;
          break;
        }
        if (!item.order) {
          result.messages = "联动导航-请填写一级导航排序";
          result.flagId = flagId;
          result.templateId = templateId;
          break;
        }
        for (const item2 of item.children) {
          if (!item2.name) {
            result.messages = "联动导航-请填写二级标题";
            result.flagId = flagId;
            result.templateId = templateId;
            break;
          }
          if (!item2.uriName && !item2.param.id) {
            result.messages = "联动导航-请选择二级标题链接";
            result.flagId = flagId;
            result.templateId = templateId;
            break;
          }
        }
      }
      for (let i = 0; i < info.list.length; i++) {
        for (let j = i + 1; j < info.list.length; j++) {
          if (info.list[i].order === info.list[j].order) {
            result.messages = "联动导航-不能填写相同的排序号";
            result.flagId = flagId;
            result.templateId = templateId;
            break;
          }
        }
      }
    }
    if (templateId === "navFlow") {
      for (const item of info.list) {
        if (!item.icon) {
          result.messages = "瀑布流导航-请上传选中前的图片";
          result.flagId = flagId;
          result.templateId = templateId;
          break;
        }
        if (!item.iconSelected) {
          result.messages = "瀑布流导航-请上传选中后的图片";
          result.flagId = flagId;
          result.templateId = templateId;
          break;
        }
        if (!item.uriName) {
          result.messages = "瀑布流导航-请选择链接";
          result.flagId = flagId;
          result.templateId = templateId;
          break;
        }
      }
    }
    if (templateId === "goods") {
      if (!info.feCategoryId) {
        result.messages = "商品-请选择商品管理类目";
        result.flagId = flagId;
        result.templateId = templateId;
        break;
      }
      if (info.style === "2") {
        for (const item of info.imgGroup) {
          if (!item.order) {
            result.messages =
              "商品-请填写图片组排序（只能填写1-99，不能填写0）";
            result.flagId = flagId;
            result.templateId = templateId;
            break;
          }
          for (const sub of item.imgs) {
            if (!sub.imgUrl) {
              result.messages = "商品-请点击上传图片";
              result.flagId = flagId;
              result.templateId = templateId;
              break;
            }
          }
        }
      }
    }
    if (templateId === "brand") {
      if (info.type === "1" && !info.platCategoryId) {
        result.messages = "品牌-请选择指定分类";
        result.flagId = flagId;
        result.templateId = templateId;
        break;
      }
      if (info.type === "2" && info.brands.length === 0) {
        result.messages = "品牌-请选择指定品牌";
        result.flagId = flagId;
        result.templateId = templateId;
        break;
      }
    }
  }
  return result;
}

export { validateDecoration };
