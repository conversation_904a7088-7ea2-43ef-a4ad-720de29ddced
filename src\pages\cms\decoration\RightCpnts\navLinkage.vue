<template>
  <div class="navLinage-content">
    <div class="navLinage-nav">
      <ul class="nav-center">
        <li
          v-for="(item, index) in navLinkagetData.list"
          :key="index"
          class="nav-li"
          :class="{ active: currentTab == index }"
          @click="selectTab(index)"
        >
          <span>{{ item.stairTitle }}</span>
          <template v-if="index != 0">
            <a-popconfirm title="确认删除吗" @confirm="shutNavOk(index)">
              <img :src="`${VITE_API_IMG}/2024/08/b51391e417c346298640dc527fa111da.png`" />
            </a-popconfirm>
          </template>
        </li>
      </ul>
      <template v-if="navLinkagetData.list.length < 4">
        <div class="navAdd-right" @click="addNavigation">
          <img :src="`${VITE_API_IMG}/2024/08/5df954a7fc7a4cc890eeec1db794ac50.png`" />
        </div>
      </template>
    </div>
    <div class="navLinage-Input-center">
      <div class="navLinage-Input-title">
        <div class="Input-title">
          <span>一级标题</span>
          <span>*</span>
        </div>
        <div class="Input-text">
          <a-input
            v-model:value="childrenData.name"
            show-count
            :maxlength="5"
            placeholder="请输入内容"
            auto-width
            @change="onChangeNavTitle"
          />
        </div>
        <div class="Input-suffix">
          <a-input-number
            v-model:value="childrenData.order"
            :max="10"
            :min="1"
            placeholder="请输入排序"
          />
        </div>
      </div>
      <template v-if="childrenData.name">
        <p class="Input-text-p">以下二级导航，支持拖动排序</p>
        <draggable
          :sort="true"
          filter=".unDrag"
          :list="childrenData.children"
          :animation="300"
          :move="onMove"
        >
          <template #item="{ element, index }">
            <div class="Input-nav-list" :class="{ unDrag: index === 0 }">
              <div class="nav-list-title">
                <div class="title">
                  <span>二级标题</span>
                  <span>*</span>
                </div>
                <div class="text-Input">
                  <a-input
                    v-model:value="element.name"
                    :maxlength="5"
                    show-count
                    placeholder="请输入内容"
                    auto-width
                  />
                </div>
              </div>
              <div class="nav-list-url">
                <div class="title">
                  <span>链接</span>
                  <span>*</span>
                </div>
                <div class="url-Input" @click="showNavPopupMethod(index)">
                  <span v-if="!element.uriName" class="url-Input-span1">请选择内容</span>
                  <span class="url-Input-span2">{{ element.uriName }}</span>
                  <img :src="`${VITE_API_IMG}/2024/08/7c67138a798d4365a216e8f2b9235fd4.png`" />
                </div>
              </div>
              <template v-if="index != 0">
                <a-popconfirm title="确认删除吗" @confirm="levelShutNav(index)">
                  <img
                    class="nav-list-shut"
                    :src="`${VITE_API_IMG}/2024/08/b48cb30452984634ba1c6114202be146.png`"
                  />
                </a-popconfirm>
              </template>
            </div>
          </template>
        </draggable>
      </template>
    </div>
    <template v-if="childrenData.name">
      <div class="navLinkage-footer" @click="addNavLink">
        <div class="footer-image">
          <img :src="`${VITE_API_IMG}/2024/08/2b7d0c791cab4b08b13244678572f7f5.png`" />
          <div class="navLinkage-addImage-tp">
            <span>添加导航{{ childrenData.children.length }}/</span>
            <span>20</span>
          </div>
        </div>
      </div>
    </template>
  </div>
  <!-- 选择发布模板 -->
  <navLinkPage ref="navLinkPageRef" @on-nav-link-call-back="onNavLinkCallBack" />
</template>
<script setup lang="ts">
  const { VITE_API_IMG } = import.meta.env;
  import { storeToRefs } from 'pinia';
  import { ref, watch, onMounted } from 'vue';
  import draggable from 'vuedraggable';
  import { getDecorationStore } from '@/store';
  import navLinkPage from './components/navLinkPage.vue';

  const decorationStore = getDecorationStore();
  const { decorationInfo } = storeToRefs(decorationStore);
  const detailData = decorationInfo.value.components.find(
    (item: any) => item.templateId === 'navLinkage',
  );
  const navLinkagetData = ref<any>({});
  navLinkagetData.value = detailData.info;
  const currentTab = ref<string | number>(0);
  const childrenData = ref<any>([]);
  childrenData.value = navLinkagetData.value.list[0];
  type navLinkPageType = { showNavLinkRef: (index) => void };
  const navLinkPageRef = ref<navLinkPageType | any>(null);
  // 新增一级导航
  const addNavigation = () => {
    if (navLinkagetData.value.list.length < 4) {
      navLinkagetData.value.list.push({
        stairTitle: '导航',
        name: null,
        order: null,
        children: [
          {
            name: null,
            uriName: null,
            uriType: 0,
            param: { id: null },
          },
        ],
      });
    }
  };
  const onChangeNavTitle = () => {
    if (!childrenData.value.name) {
      childrenData.value.children = [
        {
          name: null,
          uriName: null,
          uriType: 0,
          param: { id: null },
        },
      ];
    }
  };
  // 选择一级导航按钮
  const selectTab = index => {
    currentTab.value = index;
    childrenData.value = navLinkagetData.value.list[index];
  };
  // 删除一级导航
  const shutNavOk = index => {
    navLinkagetData.value.list.splice(index, 1);
    currentTab.value = 0;
    childrenData.value = navLinkagetData.value.list[0];
  };
  // 新增二级类目
  const addNavLink = () => {
    const idx = currentTab.value;
    const childrenList = navLinkagetData.value.list[idx].children;
    if (childrenList.length < 20) {
      childrenList.push({
        name: null,
        uriName: null,
        uriType: 0,
        param: { id: null },
      });
    }
  };
  // 删除二级导航
  const levelShutNav = index => {
    const idx = currentTab.value;
    navLinkagetData.value.list[idx].children.splice(index, 1);
  };
  const onNavLinkCallBack = item => {
    const { id, index, pageName } = item;
    console.log(childrenData.value, 'uiouio');
    childrenData.value.children[index].uriType = 0;
    childrenData.value.children[index].param.id = id;
    childrenData.value.children[index].uriName = pageName;
  };
  // 是否显示商品,品牌组件
  const showNavPopupMethod = index => {
    if (navLinkPageRef.value) {
      if (typeof navLinkPageRef.value.showNavLinkRef === 'function') {
        navLinkPageRef.value.showNavLinkRef(index);
      }
    }
  };
  // 表示不能被停靠，其他元素不可以与当前元素调换位置
  const onMove = e => {
    let isEvl = false;
    if (e.relatedContext.index === 0) isEvl = true;
    if (isEvl) return false;
  };

  watch(navLinkagetData.value, newVal => {
    newVal.list.forEach((item, index) => {
      if (index === 1) item.stairTitle = '导航二';
      if (index === 2) item.stairTitle = '导航三';
      if (index === 3) item.stairTitle = '导航四';
    });
  });

  onMounted(() => {
    navLinkagetData.value.list.forEach((item, index) => {
      if (!item.stairTitle) {
        if (index === 0) item.stairTitle = '导航一';
        if (index === 1) item.stairTitle = '导航二';
        if (index === 2) item.stairTitle = '导航三';
        if (index === 3) item.stairTitle = '导航四';
      }
    });
  });
</script>
<style lang="less" scoped>
  @import '../css/navLinkage.less';
</style>
