export interface DecorationInfo {
  id: string;
  name: string;
  type: string; // PAGE ｜ CONTENT
  scene: string; // 场景
  components: Tool[];
}

export interface Tool {
  [x: string]: any;
  id: string;
  templateName: string;
  templateId: string;
  info: any;
  flagId?: string;
  icon?: string;
}

interface decorationContent {
  title: string;
  type: string;
  scene: string;
  status: string;
  createTime: string;
}

interface decorationContentData {
  records: decorationContent[];
  total: number;
}
export interface decorationContentParams {
  page: number;
  size: number;
  pageName?: string;
  pageType?: string;
  pageSubTypeList?: any;
  pageStatus?: string;
}
export class DecorationContentTableData {
  data: decorationContentData = {
    records: [],
    total: 0,
  };

  params: decorationContentParams = {
    page: 1,
    size: 10,
    pageName: '',
    pageType: '',
    pageSubTypeList: [],
  };
}
export interface PAGINATION {
  current: number;
  pageSize: number;
  defaultCurrent: number;
  defaultPageSize: number;
  total: number;
}

export interface ImageData {
  imgUrl?: null | string;
  bgColor?: null | string;
  uriName?: null | string;
  uriType?: null | number;
  param?: Record<string, unknown>;
}
