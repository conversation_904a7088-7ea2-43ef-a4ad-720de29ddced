<template>
  <div>
    <a-row class="nav-tab">
      <a-col :span="8">全部分类<img src="@/assets/images/icon_2.png" /></a-col>
      <a-col :span="8">附近<img src="@/assets/images/icon_2.png" /></a-col>
      <a-col :span="8">排序方式<img src="@/assets/images/icon_2.png" /></a-col>
    </a-row>
    <div class="main-card">
      <div class="flex-box">
        <div class="img-css">
          <img src="@/assets/images/small-logo.png" alt="" />
        </div>
        <div class="title-box">
          <div class="title">7897897990000</div>
          <a-rate v-model:value="value" />
          <div class="desc">uyioweyurtiowe</div>
          <div class="point-box">
            <span class="point-css">
              <span class="lab">积</span>
              <span class="val">10%</span>
            </span>
            <span class="point-css">
              <span class="lab">积</span>
              <span class="val">15%</span>
            </span>
            <span class="point-css">
              <span class="lab">积</span>
              <span class="val">20%</span>
            </span>
          </div>
        </div>
        <div class="right-box">
          <div class="title">营业中</div>
          <div class="address">24.5km</div>
        </div>
      </div>
      <div class="flex-box">
        <div class="img-css">
          <img src="@/assets/images/small-logo.png" alt="" />
        </div>
        <div class="title-box">
          <div class="title">标题7897978978</div>
          <a-rate v-model:value="value" />
          <div class="desc">上海</div>
          <div class="point-box">
            <span class="point-css">
              <span class="lab">积</span>
              <span class="val">10%</span>
            </span>
            <span class="point-css">
              <span class="lab">积</span>
              <span class="val">15%</span>
            </span>
            <span class="point-css">
              <span class="lab">积</span>
              <span class="val">20%</span>
            </span>
          </div>
        </div>
        <div class="right-box">
          <div class="title">营业中</div>
          <div class="address">24.5km</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  const value = ref<number>(2);
</script>
<style scoped lang="less">
.nav-tab{
    text-align: center;
    img{
      height:14px;
    }
}
  .main-card {
    margin-top: 10px;
    .flex-box {
      display: flex;
      flex-direction: row;
      border-bottom: 1px solid #eee;
      padding: 15px 10px;
      .img-css {
        width: 90px;
        height: 90px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .title-box {
        flex: 1;
        margin-left: 12px;
        .title {
          line-height: 25px;
          padding-bottom: 0;
        }
        .desc {
          line-height: 25px;
          color: #666666;
          font-size: 12px;
        }
        .point-box {
          height: 18px;
          display: flex;
          .point-css {
            display: inline-block;
            margin-right:5px;
            .lab {
              color: #145c38;
              padding:3px;
              font-size: 11px;
              background: linear-gradient(139deg, #ceffbc 0%, #7be392 100%);
            }
            .val {
              color: #438b4e;
              font-size: 11px;
              padding:3px;
              background: #dfffe4;
            }
          }
        }
      }
      .right-box {
        width: 16%;
        .title{
            color:#D47E28;
            margin-top:25px;
        }
        .address{
            color:#333333;
            margin-top:10px;
            font-size: 12px;
        }
      }
    }
  }
</style>
