<template>
  <search-antd :form-list="formList" @on-search="handleConfirm" />
  <div class="table-border">
    <div class="table-bottom">
      <a-button type="primary" :icon="h(PlusCircleOutlined)" @click="showPagePopUp">
        新建页面
      </a-button>
    </div>
    <a-table
      :scroll="{ x: 1000 }"
      :dataSource="tableData"
      :columns="orderColumns"
      rowKey="id"
      :loading="isLoading"
      :pagination="orderPagination"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'pageStatus'">
          <template v-if="record.pageStatus === 'UNPUBLISHED'">
            <a-tag color="warning">未发布</a-tag>
          </template>
          <template v-if="record.pageStatus === 'PUBLISHED'">
            <a-tag color="default">已发布</a-tag>
          </template>
          <template v-if="record.pageStatus === 'SCHEDULED_PUBLISHED'">
            <a-tag color="processing">定时发布</a-tag>
            <div style="font-size: 12px; margin-top: 6px">
              {{ record.schedulePublishTime }}
            </div>
          </template>
        </template>
        <template v-if="column.key === 'operate'">
          <div :style="{ display: 'flex', alignItems: 'center' }">
            <template v-if="record.pageSubType === 'homePage'">
              <a-button type="link" class="btn-css" @click="goDecorationPage(record)">
                装修
              </a-button>
            </template>
            <template v-if="record.pageSubType !== 'homePage'">
              <a-button type="link" class="btn-css" @click="goDecorationPage(record)">
                装修
              </a-button>
              <a-button type="link" class="btn-css" @click="redact(record)">编辑</a-button>
              <a-button v-if="record.tag === 'old'" type="link" class="btn-css" @click="showCopyDialog(record)">复制</a-button>
            </template>
          </div>
        </template>
      </template>
      <template #emptyText>
        <div>
          <no-data />
        </div>
      </template>
    </a-table>
  </div>
  <!-- 弹框 新建页面 -->
  <a-modal
    :title="headerTitle"
    width="30%"
    :close-on-overlay-click="true"
    :visible="isShowDialog"
    @cancel="handleClose"
    @ok="clickSubmit"
  >
    <div class="dialog-pad">
      <a-form ref="formRef" layout="vertical" :rules="rulesInfo" :model="basePageFrom">
        <a-form-item label="标题" name="pageName">
          <a-input v-model:value="basePageFrom.pageName" placeholder="请输入内容" maxlength="30" />
        </a-form-item>
        <a-form-item label="类型" name="pageType">
          <a-radio-group
            v-model:value="basePageFrom.pageType"
            :disabled="isEdit"
            default-value="PAGE"
            @change="onRadioSubType"
          >
            <a-radio value="PAGE">页面</a-radio>
            <a-radio value="CONTENT">内容</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="场景" name="pageSubType">
          <template v-if="basePageFrom.pageType === 'PAGE'">
            <a-radio-group v-model:value="basePageFrom.pageSubType" :disabled="isEdit">
              <a-radio value="subPage">二级</a-radio>
            </a-radio-group>
          </template>
          <template v-if="basePageFrom.pageType === 'CONTENT'">
            <a-radio-group v-model:value="basePageFrom.pageSubType" :disabled="isEdit">
              <a-radio value="firstScreen">首屏</a-radio>
              <a-radio value="goods">商品</a-radio>
              <a-radio value="brand">品牌</a-radio>
            </a-radio-group>
          </template>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
  <!-- 复制 -->
  <a-modal
    title="复制"
    width="25%"
    :close-on-overlay-click="false"
    :visible="isShowCopyDialog"
    @cancel="handleCopyClose"
    @ok="copySubmit"
  >
    <a-form ref="copyRef" :rules="rulesCopy" :model="pageData" layout="vertical" class="mt20">
      <a-form-item label-width="150px" label="请输入复制后的名称" name="pageName">
        <a-input v-model:value="pageData.pageName" placeholder="请输入内容" maxlength="30" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'woody-ui';
  import AddCircle from '@/assets/add-circle.svg';
  import { useRoute } from 'vue-router';
  import router from '@/router';
  import noData from '@/components/Nodata/index.vue';
  import {
    sysUserPage,
    basePage,
    basePageAdd,
    basePageDetail,
    basePageupdate,
    basePageCopy,
  } from '@/api/cms/pageLayout/index';
  import { ORDER_COLUMNS, FROM_DATA, ORDER_PAGETION, BASE_PAGE } from './index';
  import { h } from 'vue';
  import { PlusCircleOutlined } from '@ant-design/icons-vue';
  import SearchAntd from '@/components/SearchAntd/index.vue';

  const route = useRoute();
  const fromData = reactive({ ...FROM_DATA });
  const orderColumns = reactive(ORDER_COLUMNS);
  const orderPagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: total => `共${total}条数据`,
  });
  const optionsList = ref<any[]>([]);
  const isShowDialog = ref(false);
  const isLoading = ref<boolean>(false);
  const tableData = ref<any[]>([]);
  const basePageFrom = reactive(BASE_PAGE);
  const formRef = ref<any>(null);
  const headerTitle = ref<string | null>('新建页面');
  const headerType = ref<string | null>('ADD');
  const isEdit = ref<boolean>(false);
  const isShowCopyDialog = ref<boolean>(false);
  const pageData = reactive<any>({ pageName: null, id: null });
  const copyRef = ref<any>(null);
  const rulesCopy = reactive<any>({
    pageName: [{ required: true, message: '请输入内容' }],
  });
  const rulesInfo = reactive<any>({
    pageName: [{ required: true, message: '请输入标题' }],
    pageType: [{ required: true, message: '请选择类型' }],
    pageSubType: [{ required: true, message: '请选择场景' }],
  });
  const remoteMethod = search => {
    httpSysUserPage(search);
  };
  const formList = [
    {
      label: '页面标题',
      name: 'pageName',
      type: 'input',
      span: 6,
    },
    {
      label: '创建人',
      name: 'createUserId',
      type: 'select',
      options: optionsList,
      showSearch: true,
      remoteMethod,
      span: 6,
    },
    {
      label: '创建日期',
      name: 'createTime',
      type: 'datePicker',
      showTime: true,
      span: 6,
    },
    {
      label: '更新日期',
      name: 'updateTime',
      type: 'datePicker',
      showTime: true,
      span: 6,
    },
    {
      label: '页面类型',
      name: 'pageType',
      type: 'select',
      options: [
        {
          label: '页面',
          value: 'PAGE',
        },
        {
          label: '内容',
          value: 'CONTENT',
        },
      ],
      span: 6,
    },
    {
      label: '模板',
      name: 'pageSubTypeList',
      type: 'select',
      options: [
        {
          label: '二级',
          value: 'subPage',
        },
        {
          label: '首屏',
          value: 'firstScreen',
        },
        {
          label: '商品',
          value: 'goods',
        },
        {
          label: '品牌',
          value: 'brand',
        },
      ],
      span: 6,
    },
    {
      label: '页面状态',
      name: 'pageStatus',
      type: 'select',
      options: [
        {
          label: '未发布',
          value: 'UNPUBLISHED',
        },
        {
          label: '已发布',
          value: 'PUBLISHED',
        },
        {
          label: '定时发布',
          value: 'SCHEDULED_PUBLISHED',
        },
      ],
      span: 6,
    },
  ];
  // 查询所有用户信息
  const httpSysUserPage = (username?: string) => {
    const params = { page: 1, size: 50, username };
    sysUserPage(params).then(res => {
      console.log(res, 'res123');
      if (res.code === 0) {
        optionsList.value = (res.data.records || []).map(item => {
          return {
            label: item.username,
            value: item.userId,
          };
        });
      }
    });
  };
  // 查询页面配置列表
  const httpBasePage = () => {
    isLoading.value = true;
    const { current, pageSize } = orderPagination.value;
    const { createTime, createUserId, pageName, pageStatus, pageType, updateTime } = fromData;
    const list = fromData.pageSubTypeList ? [fromData.pageSubTypeList] : null;
    const params = {
      createTime,
      createUserId,
      pageName,
      pageStatus,
      pageType,
      pageSubTypeList: list,
      updateTime,
      current,
      pageSize,
    };
    basePage(params)
      .then(res => {
        if (res.code === 0) {
          tableData.value = res.data.records;
          orderPagination.value.total = res.data.total;
        }
      })
      .finally(() => {
        isLoading.value = false;
      });
  };
  // 分页
  const handleTableChange = newPagination => {
    orderPagination.value = { ...orderPagination.value, ...newPagination };
    httpBasePage();
  };
  // 查询页面配置列表
  const handleConfirm = params => {
    Object.assign(fromData, FROM_DATA, params);
    orderPagination.value.current = 1;
    orderPagination.value.pageSize = 10;
    httpBasePage();
  };
  // 重置页面配置列表
  const settingAside = () => {
    fromData.pageName = null;
    fromData.pageStatus = null;
    fromData.createUserId = null;
    fromData.pageSubTypeList = null;
    fromData.pageType = null;
    fromData.updateTime = null;
    fromData.createTime = null;
    orderPagination.value.current = 1;
    orderPagination.value.pageSize = 10;
    try {
      httpBasePage();
    } catch (err) {
      console.error('Error occurred while calling httpBasePage:', err);
    }
  };
  const goDecorationPage = (rowData: any) => {
    // router.push(`/cms/decoration/${rowData.id}`); 老装修页面
    router.push(`/cms/decorationV2/${rowData.id}`);
  };
  // 编辑列表
  const redact = row => {
    basePageDetail({ id: row.id }).then(res => {
      if (res.code === 0) {
        const { pageName, pageType, pageSubType, id } = res.data;
        headerTitle.value = '编辑页面';
        headerType.value = 'EDIT';
        basePageFrom.pageName = pageName;
        basePageFrom.pageType = pageType;
        basePageFrom.pageSubType = pageSubType;
        basePageFrom.createUserId = id;
        isEdit.value = true;
        isShowDialog.value = true;
      }
    });
  };
  const onRadioSubType = () => {
    if (basePageFrom.pageType === 'PAGE') {
      basePageFrom.pageSubType = 'subPage';
    }
    if (basePageFrom.pageType === 'CONTENT') {
      basePageFrom.pageSubType = 'firstScreen';
    }
  };
  // 新建,编辑页面
  const clickSubmit = () => {
    formRef.value.validate().then(() => {
      if (headerType.value === 'ADD') {
        basePageAdd(basePageFrom).then(res => {
          if (res.code === 0) {
            message.success('新增成功！');
            settingAside();
            isShowDialog.value = false;
          } else {
            message.error(res.message);
          }
        });
      } else {
        const { createUserId, pageName } = basePageFrom;
        const params = { id: createUserId, pageName };
        basePageupdate(params).then(res => {
          if (res.code === 0) {
            message.success('编辑成功！');
            settingAside();
            isShowDialog.value = false;
          } else {
            message.error(res.message);
          }
        });
      }
    });
  };
  // 关闭新建页面弹框
  const handleClose = () => {
    formRef.value.clearValidate();
    isShowDialog.value = false;
  };
  // 显示复制弹框
  const showCopyDialog = row => {
    pageData.id = row.id;
    pageData.pageName = null;
    isShowCopyDialog.value = true;
  };
  // 复制
  const copySubmit = () => {
    copyRef.value.validate().then(() => {
      basePageCopy(pageData).then(res => {
        if (res.code === 0) {
          message.success('复制成功！');
          orderPagination.value.current = 1;
          orderPagination.value.pageSize = 10;
          httpBasePage();
          isShowCopyDialog.value = false;
        } else {
          message.error(res.message);
        }
      });
    });
  };
  // 关闭复制弹框
  const handleCopyClose = () => {
    copyRef.value.clearValidate();
    isShowCopyDialog.value = false;
  };
  // 显示新建页面弹框
  const showPagePopUp = () => {
    headerTitle.value = '新建页面';
    headerType.value = 'ADD';
    isEdit.value = false;
    isShowDialog.value = true;
    basePageFrom.pageName = null;
    basePageFrom.pageType = 'PAGE';
    basePageFrom.pageSubType = 'subPage';
    basePageFrom.createUserId = null;
  };

  onMounted(() => {
    orderPagination.value.current = 1;
    orderPagination.value.pageSize = 10;
    httpSysUserPage();
    httpBasePage();
    if (route.query.openModal) {
      showPagePopUp();
    }
  });
</script>
<style lang="less" scoped>
  .section-top {
    border-radius: 16px;
    background: #fff;
    padding: 32px;
  }

  .t-table__content--scrollable-to-right {
    :deep(.t-table__cell--fixed-right-first) {
      &::after {
        border-left: 5px solid @table-boder-color;
      }
    }
  }

  .table-border {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-radius: 16px;
    background: #fff;
    padding-left: 32px;
    padding-right: 32px;
    margin-top: 16px;

    .table-bottom {
      padding-top: 32px;
      padding-bottom: 16px;
      text-align: right;

      svg {
        margin-right: 3px;
      }
    }
  }

  // .btn-css{
  //   color:var(--td-brand-color);
  // }

  :deep(th) {
    background-color: @table-th-bg !important;
    color: @table-th-color;
    font-weight: 400;

    &:first-child {
      border-top-left-radius: 8px;
    }

    &:last-child {
      border-top-right-radius: 8px;
    }
  }

  :deep(td) {
    border-bottom-color: @table-boder-color;
  }

  :deep(.t-table__pagination) {
    padding-top: 28px;

    .t-pagination {
      .t-pagination__jump {
        margin-left: 16px;
      }
    }
  }
</style>
